import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3 } from "lucide-react"

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Statistiques et analyses</h1>
        <p className="text-gray-600">
          Visualisez les données et tendances de vos audits
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Module en développement
          </CardTitle>
          <CardDescription>
            Le module de statistiques sera bientôt disponible
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            Cette section affichera des graphiques et analyses détaillées.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
