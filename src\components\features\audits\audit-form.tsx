"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  CreateAuditInput, 
  UpdateAuditInput, 
  createAuditSchema, 
  updateAuditSchema,
  AuditWithRelations,
  AuditStatus
} from "@/lib/validations/audit"
import { Calendar, Users, AlertCircle, Loader2 } from "lucide-react"
import { format } from "date-fns"

interface Organization {
  id: string
  name: string
}

interface User {
  id: string
  name: string | null
  email: string
  role: string
}

interface AuditFormProps {
  audit?: AuditWithRelations
  organizations: Organization[]
  users: User[]
  onSubmit: (data: CreateAuditInput | UpdateAuditInput) => Promise<void>
  loading?: boolean
  error?: string | null
}

export function AuditForm({ 
  audit, 
  organizations, 
  users, 
  onSubmit, 
  loading = false, 
  error 
}: AuditFormProps) {
  const [selectedAuditors, setSelectedAuditors] = useState<string[]>([])
  const [leadAuditorId, setLeadAuditorId] = useState<string>("")

  const isEditing = !!audit

  const form = useForm<CreateAuditInput | UpdateAuditInput>({
    resolver: zodResolver(isEditing ? updateAuditSchema : createAuditSchema),
    defaultValues: isEditing ? {
      title: audit.title,
      description: audit.description || "",
      startDate: format(audit.startDate, "yyyy-MM-dd'T'HH:mm"),
      endDate: audit.endDate ? format(audit.endDate, "yyyy-MM-dd'T'HH:mm") : "",
      organizationId: audit.organizationId,
      status: audit.status
    } : {
      title: "",
      description: "",
      startDate: "",
      endDate: "",
      organizationId: "",
    }
  })

  // Initialiser les auditeurs sélectionnés pour l'édition
  useEffect(() => {
    if (audit) {
      const auditorIds = audit.auditors.map(a => a.user.id)
      const leadAuditor = audit.auditors.find(a => a.role === "LEAD_AUDITOR")
      
      setSelectedAuditors(auditorIds)
      if (leadAuditor) {
        setLeadAuditorId(leadAuditor.user.id)
      }
    }
  }, [audit])

  const handleAuditorToggle = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedAuditors(prev => [...prev, userId])
    } else {
      setSelectedAuditors(prev => prev.filter(id => id !== userId))
      // Si c'était l'auditeur principal, le retirer
      if (leadAuditorId === userId) {
        setLeadAuditorId("")
      }
    }
  }

  const handleLeadAuditorChange = (userId: string) => {
    setLeadAuditorId(userId)
    // S'assurer que l'auditeur principal est dans la liste des auditeurs
    if (!selectedAuditors.includes(userId)) {
      setSelectedAuditors(prev => [...prev, userId])
    }
  }

  const handleSubmit = async (data: CreateAuditInput | UpdateAuditInput) => {
    if (!isEditing) {
      // Pour la création, ajouter les auditeurs
      const createData = data as CreateAuditInput
      createData.auditorIds = selectedAuditors
      createData.leadAuditorId = leadAuditorId
    }

    await onSubmit(data)
  }

  const availableUsers = users.filter(user => 
    user.role === "AUDITOR" || user.role === "MANAGER" || user.role === "ADMIN"
  )

  const selectedLeadAuditorOptions = selectedAuditors.map(id => {
    const user = users.find(u => u.id === id)
    return user ? { id: user.id, name: user.name || user.email } : null
  }).filter(Boolean)

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Informations générales */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Informations générales
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titre de l'audit *</Label>
              <Input
                id="title"
                {...form.register("title")}
                placeholder="Ex: Audit qualité ISO 9001"
                disabled={loading}
              />
              {form.formState.errors.title && (
                <p className="text-sm text-red-600">{form.formState.errors.title.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="organizationId">Organisation *</Label>
              <Select
                value={form.watch("organizationId")}
                onValueChange={(value) => form.setValue("organizationId", value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une organisation" />
                </SelectTrigger>
                <SelectContent>
                  {organizations.map((org) => (
                    <SelectItem key={org.id} value={org.id}>
                      {org.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.organizationId && (
                <p className="text-sm text-red-600">{form.formState.errors.organizationId.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="Description détaillée de l'audit..."
              rows={3}
              disabled={loading}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Date de début *</Label>
              <Input
                id="startDate"
                type="datetime-local"
                {...form.register("startDate")}
                disabled={loading}
              />
              {form.formState.errors.startDate && (
                <p className="text-sm text-red-600">{form.formState.errors.startDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">Date de fin</Label>
              <Input
                id="endDate"
                type="datetime-local"
                {...form.register("endDate")}
                disabled={loading}
              />
              {form.formState.errors.endDate && (
                <p className="text-sm text-red-600">{form.formState.errors.endDate.message}</p>
              )}
            </div>
          </div>

          {isEditing && (
            <div className="space-y-2">
              <Label htmlFor="status">Statut</Label>
              <Select
                value={form.watch("status")}
                onValueChange={(value) => form.setValue("status", value as any)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={AuditStatus.PLANNED}>Planifié</SelectItem>
                  <SelectItem value={AuditStatus.IN_PROGRESS}>En cours</SelectItem>
                  <SelectItem value={AuditStatus.COMPLETED}>Terminé</SelectItem>
                  <SelectItem value={AuditStatus.ON_HOLD}>En attente</SelectItem>
                  <SelectItem value={AuditStatus.CANCELLED}>Annulé</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.status && (
                <p className="text-sm text-red-600">{form.formState.errors.status.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Assignation des auditeurs */}
      {!isEditing && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Assignation des auditeurs
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Label>Sélectionner les auditeurs *</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
                {availableUsers.map((user) => (
                  <div key={user.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`auditor-${user.id}`}
                      checked={selectedAuditors.includes(user.id)}
                      onCheckedChange={(checked) => handleAuditorToggle(user.id, checked as boolean)}
                      disabled={loading}
                    />
                    <Label htmlFor={`auditor-${user.id}`} className="flex-1 cursor-pointer">
                      <div>
                        <p className="font-medium">{user.name || user.email}</p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {selectedAuditors.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="leadAuditor">Auditeur principal *</Label>
                <Select
                  value={leadAuditorId}
                  onValueChange={handleLeadAuditorChange}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner l'auditeur principal" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedLeadAuditorOptions.map((auditor) => (
                      <SelectItem key={auditor!.id} value={auditor!.id}>
                        {auditor!.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedAuditors.length === 0 && (
              <p className="text-sm text-red-600">Au moins un auditeur doit être sélectionné</p>
            )}
            {selectedAuditors.length > 0 && !leadAuditorId && (
              <p className="text-sm text-red-600">Un auditeur principal doit être désigné</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <div className="flex items-center justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => window.history.back()}
          disabled={loading}
        >
          Annuler
        </Button>
        
        <Button
          type="submit"
          disabled={loading || (!isEditing && (selectedAuditors.length === 0 || !leadAuditorId))}
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isEditing ? "Mettre à jour" : "Créer l'audit"}
        </Button>
      </div>
    </form>
  )
}
