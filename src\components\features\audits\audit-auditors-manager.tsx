"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AuditWithRelations, AssignAuditorsInput, AuditUserRole } from "@/lib/validations/audit"
import { Users, Edit, AlertCircle, Loader2 } from "lucide-react"

interface User {
  id: string
  name: string | null
  email: string
  role: string
}

interface AuditAuditorsManagerProps {
  audit: AuditWithRelations
  users: User[]
  onAssignAuditors: (data: AssignAuditorsInput) => Promise<void>
  loading?: boolean
  error?: string | null
}

export function AuditAuditorsManager({ 
  audit, 
  users, 
  onAssignAuditors, 
  loading = false, 
  error 
}: AuditAuditorsManagerProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedAuditors, setSelectedAuditors] = useState<string[]>([])
  const [auditorRoles, setAuditorRoles] = useState<Record<string, string>>({})

  const availableUsers = users.filter(user => 
    user.role === "AUDITOR" || user.role === "MANAGER" || user.role === "ADMIN"
  )

  const handleOpenDialog = () => {
    // Initialiser avec les auditeurs actuels
    const currentAuditorIds = audit.auditors.map(a => a.user.id)
    const currentRoles = audit.auditors.reduce((acc, a) => {
      acc[a.user.id] = a.role
      return acc
    }, {} as Record<string, string>)

    setSelectedAuditors(currentAuditorIds)
    setAuditorRoles(currentRoles)
    setIsDialogOpen(true)
  }

  const handleAuditorToggle = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedAuditors(prev => [...prev, userId])
      // Assigner le rôle par défaut
      setAuditorRoles(prev => ({
        ...prev,
        [userId]: AuditUserRole.AUDITOR
      }))
    } else {
      setSelectedAuditors(prev => prev.filter(id => id !== userId))
      setAuditorRoles(prev => {
        const newRoles = { ...prev }
        delete newRoles[userId]
        return newRoles
      })
    }
  }

  const handleRoleChange = (userId: string, role: string) => {
    setAuditorRoles(prev => ({
      ...prev,
      [userId]: role
    }))
  }

  const handleSubmit = async () => {
    // Vérifier qu'il y a exactement un auditeur principal
    const leadAuditors = Object.values(auditorRoles).filter(role => role === AuditUserRole.LEAD_AUDITOR)
    
    if (leadAuditors.length !== 1) {
      alert("Il doit y avoir exactement un auditeur principal")
      return
    }

    const assignmentData: AssignAuditorsInput = {
      auditorAssignments: selectedAuditors.map(userId => ({
        userId,
        role: auditorRoles[userId] as any
      }))
    }

    await onAssignAuditors(assignmentData)
    setIsDialogOpen(false)
  }

  const leadAuditors = Object.entries(auditorRoles).filter(([_, role]) => role === AuditUserRole.LEAD_AUDITOR)
  const isValid = selectedAuditors.length > 0 && leadAuditors.length === 1

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Équipe d'audit ({audit.auditors.length})
          </CardTitle>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" onClick={handleOpenDialog}>
                <Edit className="h-4 w-4 mr-2" />
                Modifier l'équipe
              </Button>
            </DialogTrigger>
            
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Modifier l'équipe d'audit</DialogTitle>
                <DialogDescription>
                  Sélectionnez les auditeurs et assignez leurs rôles
                </DialogDescription>
              </DialogHeader>

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                <div className="space-y-3">
                  <Label>Sélectionner les auditeurs</Label>
                  <div className="space-y-3 max-h-60 overflow-y-auto">
                    {availableUsers.map((user) => (
                      <div key={user.id} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`auditor-${user.id}`}
                            checked={selectedAuditors.includes(user.id)}
                            onCheckedChange={(checked) => handleAuditorToggle(user.id, checked as boolean)}
                            disabled={loading}
                          />
                          <Label htmlFor={`auditor-${user.id}`} className="flex-1 cursor-pointer">
                            <div>
                              <p className="font-medium">{user.name || user.email}</p>
                              <p className="text-sm text-gray-500">{user.email}</p>
                            </div>
                          </Label>
                        </div>
                        
                        {selectedAuditors.includes(user.id) && (
                          <div className="ml-6">
                            <Select
                              value={auditorRoles[user.id] || AuditUserRole.AUDITOR}
                              onValueChange={(value) => handleRoleChange(user.id, value)}
                              disabled={loading}
                            >
                              <SelectTrigger className="w-48">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value={AuditUserRole.LEAD_AUDITOR}>
                                  Auditeur principal
                                </SelectItem>
                                <SelectItem value={AuditUserRole.AUDITOR}>
                                  Auditeur
                                </SelectItem>
                                <SelectItem value={AuditUserRole.OBSERVER}>
                                  Observateur
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {!isValid && selectedAuditors.length > 0 && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Il doit y avoir exactement un auditeur principal.
                      Actuellement: {leadAuditors.length} auditeur(s) principal(aux).
                    </AlertDescription>
                  </Alert>
                )}
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  disabled={loading}
                >
                  Annuler
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={loading || !isValid}
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Enregistrer
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-3">
          {audit.auditors.map((auditor) => (
            <div key={auditor.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">{auditor.user.name || auditor.user.email}</p>
                <p className="text-sm text-gray-500">{auditor.user.email}</p>
              </div>
              <Badge variant={auditor.role === "LEAD_AUDITOR" ? "default" : "secondary"}>
                {auditor.role === "LEAD_AUDITOR" ? "Auditeur principal" : 
                 auditor.role === "AUDITOR" ? "Auditeur" : "Observateur"}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
