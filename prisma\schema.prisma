generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

model User {
  id              String        @id @default(cuid())
  email           String        @unique
  name            String?
  password        String?
  role            String        @default("USER")
  isActive        Boolean       @default(true)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  lastLoginAt     DateTime?
  organizationId  String?
  emailVerified   Boolean
  image           String?
  accounts        Account[]
  assignedActions Action[]      @relation("ActionAssignee")
  createdActions  Action[]      @relation("ActionCreator")
  auditorsOn      AuditUser[]
  createdAudits   Audit[]       @relation("AuditCreator")
  createdReports  Report[]      @relation("ReportCreator")
  sessions        Session[]
  organization    Organization? @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("users")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  audits      Audit[]
  reports     Report[]
  users       User[]

  @@map("organizations")
}

model Audit {
  id             String        @id @default(cuid())
  title          String
  description    String?
  status         String        @default("PLANNED")
  startDate      DateTime
  endDate        DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  organizationId String
  creatorId      String
  actions        Action[]
  auditors       AuditUser[]
  creator        User          @relation("AuditCreator", fields: [creatorId], references: [id], onUpdate: NoAction)
  organization   Organization  @relation(fields: [organizationId], references: [id], onUpdate: NoAction)
  observations   Observation[]
  reports        Report[]

  @@map("audits")
}

model AuditUser {
  id      String @id @default(cuid())
  role    String @default("AUDITOR")
  auditId String
  userId  String
  audit   Audit  @relation(fields: [auditId], references: [id], onDelete: Cascade)
  user    User   @relation(fields: [userId], references: [id], onUpdate: NoAction)

  @@unique([auditId, userId])
  @@map("audit_users")
}

model Observation {
  id          String   @id @default(cuid())
  title       String
  description String
  severity    String   @default("LOW")
  status      String   @default("OPEN")
  evidence    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  auditId     String
  actions     Action[]
  audit       Audit    @relation(fields: [auditId], references: [id], onDelete: Cascade)

  @@map("observations")
}

model Action {
  id            String       @id @default(cuid())
  title         String
  description   String
  dueDate       DateTime
  status        String       @default("PENDING")
  priority      String       @default("MEDIUM")
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  completedAt   DateTime?
  auditId       String
  observationId String?
  assigneeId    String
  creatorId     String
  assignee      User         @relation("ActionAssignee", fields: [assigneeId], references: [id], onUpdate: NoAction)
  audit         Audit        @relation(fields: [auditId], references: [id], onUpdate: NoAction)
  creator       User         @relation("ActionCreator", fields: [creatorId], references: [id], onUpdate: NoAction)
  observation   Observation? @relation(fields: [observationId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("actions")
}

model Report {
  id             String       @id @default(cuid())
  title          String
  content        String
  status         String       @default("DRAFT")
  version        Int          @default(1)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  publishedAt    DateTime?
  organizationId String
  auditId        String
  creatorId      String
  audit          Audit        @relation(fields: [auditId], references: [id], onUpdate: NoAction)
  creator        User         @relation("ReportCreator", fields: [creatorId], references: [id], onUpdate: NoAction)
  organization   Organization @relation(fields: [organizationId], references: [id], onUpdate: NoAction)

  @@map("reports")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}
