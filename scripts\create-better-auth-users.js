const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createBetterAuthUsers() {
  try {
    console.log('🔄 Creating Better Auth users...')
    
    // First, let's clean up existing users to avoid conflicts
    console.log('🧹 Cleaning up existing users...')
    await prisma.session.deleteMany({})
    await prisma.account.deleteMany({})
    await prisma.user.deleteMany({})
    
    // Test users to create
    const testUsers = [
      {
        email: '<EMAIL>',
        name: 'Administrateur Test',
        password: 'password123',
        role: 'SUPER_ADMIN'
      },
      {
        email: '<EMAIL>',
        name: 'Gestionnaire Test',
        password: 'password123',
        role: 'MANAGER'
      },
      {
        email: '<EMAIL>',
        name: 'Auditeur Test',
        password: 'password123',
        role: 'AUDITOR'
      },
      {
        email: '<EMAIL>',
        name: 'Utilisateur Test',
        password: 'password123',
        role: 'USER'
      }
    ]
    
    console.log('📝 Creating users via Better Auth API...')
    
    for (const userData of testUsers) {
      try {
        // Make a POST request to the Better Auth sign-up endpoint
        const response = await fetch('http://localhost:8080/api/auth/sign-up/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: userData.email,
            password: userData.password,
            name: userData.name,
            role: userData.role,
            isActive: true,
            emailVerified: true
          })
        })
        
        if (response.ok) {
          const result = await response.json()
          console.log(`✅ User created via Better Auth: ${userData.email} (${userData.role})`)
          
          // Update the user with additional fields that Better Auth might not handle
          if (result.data && result.data.user && result.data.user.id) {
            await prisma.user.update({
              where: { id: result.data.user.id },
              data: {
                role: userData.role,
                isActive: true,
                emailVerified: true
              }
            })
            console.log(`🔧 Updated user fields for: ${userData.email}`)
          }
        } else {
          const error = await response.text()
          console.error(`❌ Failed to create user ${userData.email}:`, error)
        }
      } catch (error) {
        console.error(`❌ Error creating user ${userData.email}:`, error.message)
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    console.log('\n📋 Better Auth Users Summary:')
    console.log('📧 All emails use password: password123')
    console.log('👤 <EMAIL> (SUPER_ADMIN)')
    console.log('👤 <EMAIL> (MANAGER)')
    console.log('👤 <EMAIL> (AUDITOR)')
    console.log('👤 <EMAIL> (USER)')
    console.log('\n🔐 Users created with Better Auth compatible password hashing')
    
  } catch (error) {
    console.error('❌ Error in createBetterAuthUsers:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createBetterAuthUsers()
