"use client"

import { useState, useEffect } from "react"

interface Organization {
  id: string
  name: string
}

interface User {
  id: string
  name: string | null
  email: string
  role: string
}

interface Audit {
  id: string
  title: string
  organization: {
    name: string
  }
}

interface UseFormDataResult {
  organizations: Organization[]
  users: User[]
  audits: Audit[]
  loading: boolean
  error: string | null
}

export function useFormData(): UseFormDataResult {
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [audits, setAudits] = useState<Audit[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Récupérer les organisations, utilisateurs et audits en parallèle
        const [orgsResponse, usersResponse, auditsResponse] = await Promise.all([
          fetch("/api/organizations"),
          fetch("/api/users"),
          fetch("/api/audits")
        ])

        if (!orgsResponse.ok || !usersResponse.ok || !auditsResponse.ok) {
          throw new Error("Erreur lors du chargement des données")
        }

        const [orgsData, usersData, auditsData] = await Promise.all([
          orgsResponse.json(),
          usersResponse.json(),
          auditsResponse.json()
        ])

        if (orgsData.success) {
          setOrganizations(orgsData.data.organizations || orgsData.data || [])
        }

        if (usersData.success) {
          setUsers(usersData.data.users || usersData.data || [])
        }

        if (auditsData.success) {
          setAudits(auditsData.data.audits || auditsData.data || [])
        }

      } catch (err) {
        console.error("Erreur lors du chargement des données:", err)
        setError(err instanceof Error ? err.message : "Erreur inconnue")
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  return {
    organizations,
    users,
    audits,
    loading,
    error
  }
}
