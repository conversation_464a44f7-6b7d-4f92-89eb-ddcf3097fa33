"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  AuditTable,
  AuditFiltersComponent,
  AuditPagination
} from "@/components/features/audits"
import { useAudits } from "@/hooks/use-audits"
import { useAuditActions } from "@/hooks/use-audit-actions"
import { useAuditPermissions } from "@/hooks/use-audit-permissions"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Plus, Download, Trash2, AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"

export default function AuditsPage() {
  const router = useRouter()
  const [selectedAuditIds, setSelectedAuditIds] = useState<string[]>([])

  const {
    audits,
    total,
    totalPages,
    loading,
    error,
    filters,
    setFilters,
    refetch
  } = useAudits()

  const {
    loading: actionLoading,
    error: actionError,
    deleteAudit,
    clearError
  } = useAuditActions()

  const {
    canCreate,
    canUpdate,
    canDelete,
    user
  } = useAuditPermissions()

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handleItemsPerPageChange = (limit: number) => {
    setFilters({ ...filters, limit, page: 1 })
  }

  const handleViewAudit = (audit: AuditWithRelations) => {
    router.push(`/audits/${audit.id}`)
  }

  const handleEditAudit = (audit: AuditWithRelations) => {
    router.push(`/audits/${audit.id}/edit`)
  }

  const handleDeleteAudit = async (audit: AuditWithRelations) => {
    if (!canDelete(audit)) {
      alert("Vous n'avez pas les permissions pour supprimer cet audit")
      return
    }

    if (confirm(`Êtes-vous sûr de vouloir supprimer l'audit "${audit.title}" ?`)) {
      const success = await deleteAudit(audit.id)
      if (success) {
        await refetch()
      }
    }
  }

  const handleBulkDelete = async () => {
    if (selectedAuditIds.length === 0) return

    if (confirm(`Êtes-vous sûr de vouloir supprimer ${selectedAuditIds.length} audit(s) ?`)) {
      // Supprimer chaque audit sélectionné
      for (const auditId of selectedAuditIds) {
        await deleteAudit(auditId)
      }
      setSelectedAuditIds([])
      await refetch()
    }
  }

  const handleCreateAudit = () => {
    router.push("/audits/new")
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold magneto-title">Gestion des audits</h1>
          <p className="text-gray-600">
            Gérez vos audits et suivez leur progression
          </p>
        </div>

        <div className="flex items-center gap-2">
          {selectedAuditIds.length > 0 && canDelete() && (
            <Button
              variant="outline"
              onClick={handleBulkDelete}
              disabled={actionLoading}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Supprimer ({selectedAuditIds.length})
            </Button>
          )}

          <Button
            variant="outline"
            onClick={() => {/* TODO: Implémenter l'export */}}
            disabled={loading}
          >
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </Button>

          {canCreate() && (
            <Button onClick={handleCreateAudit}>
              <Plus className="h-4 w-4 mr-2" />
              Nouvel audit
            </Button>
          )}
        </div>
      </div>

      {/* Erreurs */}
      {(error || actionError) && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || actionError}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Filtres */}
      <AuditFiltersComponent
        filters={filters}
        onFiltersChange={setFilters}
        loading={loading}
      />

      {/* Tableau des audits */}
      <Card>
        <CardHeader>
          <CardTitle>
            Audits ({total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <AuditTable
              audits={audits}
              onView={handleViewAudit}
              onEdit={handleEditAudit}
              onDelete={handleDeleteAudit}
              onSelectionChange={setSelectedAuditIds}
              loading={loading}
            />

            {total > 0 && (
              <AuditPagination
                currentPage={filters.page}
                totalPages={totalPages}
                totalItems={total}
                itemsPerPage={filters.limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                loading={loading}
              />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
