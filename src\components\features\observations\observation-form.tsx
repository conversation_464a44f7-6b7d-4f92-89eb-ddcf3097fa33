"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  CreateObservationInput, 
  UpdateObservationInput, 
  createObservationSchema, 
  updateObservationSchema,
  ObservationWithRelations,
  ObservationSeverity,
  ObservationStatus
} from "@/lib/validations/observation"
import { AlertTriangle, Calendar, FileText, Loader2 } from "lucide-react"

interface Audit {
  id: string
  title: string
  organization: {
    name: string
  }
}

interface ObservationFormProps {
  observation?: ObservationWithRelations
  audits: Audit[]
  onSubmit: (data: CreateObservationInput | UpdateObservationInput) => Promise<void>
  loading?: boolean
  error?: string | null
  defaultAuditId?: string
}

export function ObservationForm({ 
  observation, 
  audits, 
  onSubmit, 
  loading = false, 
  error,
  defaultAuditId
}: ObservationFormProps) {
  const isEditing = !!observation

  const form = useForm<CreateObservationInput | UpdateObservationInput>({
    resolver: zodResolver(isEditing ? updateObservationSchema : createObservationSchema),
    defaultValues: isEditing ? {
      title: observation.title,
      description: observation.description,
      severity: observation.severity,
      status: observation.status,
      evidence: observation.evidence || ""
    } : {
      title: "",
      description: "",
      severity: ObservationSeverity.MEDIUM,
      evidence: "",
      auditId: defaultAuditId || ""
    }
  })

  const handleSubmit = async (data: CreateObservationInput | UpdateObservationInput) => {
    await onSubmit(data)
  }

  const selectedAudit = audits.find(audit => audit.id === form.watch("auditId"))

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Informations générales */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Informations générales
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Audit (seulement pour la création) */}
          {!isEditing && (
            <div className="space-y-2">
              <Label htmlFor="auditId">Audit concerné *</Label>
              <Select
                value={form.watch("auditId")}
                onValueChange={(value) => form.setValue("auditId", value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un audit" />
                </SelectTrigger>
                <SelectContent>
                  {audits.map((audit) => (
                    <SelectItem key={audit.id} value={audit.id}>
                      <div>
                        <div className="font-medium">{audit.title}</div>
                        <div className="text-xs text-gray-500">{audit.organization.name}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.auditId && (
                <p className="text-sm text-red-600">{form.formState.errors.auditId.message}</p>
              )}
              
              {selectedAudit && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-900">{selectedAudit.title}</p>
                  <p className="text-xs text-blue-700">{selectedAudit.organization.name}</p>
                </div>
              )}
            </div>
          )}

          {/* Titre */}
          <div className="space-y-2">
            <Label htmlFor="title">Titre de l'observation *</Label>
            <Input
              id="title"
              {...form.register("title")}
              placeholder="Ex: Non-conformité sur la gestion documentaire"
              disabled={loading}
            />
            {form.formState.errors.title && (
              <p className="text-sm text-red-600">{form.formState.errors.title.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description détaillée *</Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="Décrivez précisément l'observation, les faits constatés, les écarts identifiés..."
              rows={4}
              disabled={loading}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
            )}
          </div>

          {/* Sévérité et Statut */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="severity">Sévérité *</Label>
              <Select
                value={form.watch("severity")}
                onValueChange={(value) => form.setValue("severity", value as any)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner la sévérité" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ObservationSeverity.CRITICAL}>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      Critique
                    </div>
                  </SelectItem>
                  <SelectItem value={ObservationSeverity.HIGH}>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                      Élevée
                    </div>
                  </SelectItem>
                  <SelectItem value={ObservationSeverity.MEDIUM}>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                      Moyenne
                    </div>
                  </SelectItem>
                  <SelectItem value={ObservationSeverity.LOW}>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      Faible
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.severity && (
                <p className="text-sm text-red-600">{form.formState.errors.severity.message}</p>
              )}
            </div>

            {/* Statut (seulement pour l'édition) */}
            {isEditing && (
              <div className="space-y-2">
                <Label htmlFor="status">Statut</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) => form.setValue("status", value as any)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner le statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ObservationStatus.OPEN}>Ouverte</SelectItem>
                    <SelectItem value={ObservationStatus.IN_PROGRESS}>En cours</SelectItem>
                    <SelectItem value={ObservationStatus.RESOLVED}>Résolue</SelectItem>
                    <SelectItem value={ObservationStatus.CLOSED}>Fermée</SelectItem>
                    <SelectItem value={ObservationStatus.REJECTED}>Rejetée</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.status && (
                  <p className="text-sm text-red-600">{form.formState.errors.status.message}</p>
                )}
              </div>
            )}
          </div>

          {/* Preuves */}
          <div className="space-y-2">
            <Label htmlFor="evidence">Preuves et éléments observés</Label>
            <Textarea
              id="evidence"
              {...form.register("evidence")}
              placeholder="Décrivez les preuves, documents consultés, témoignages recueillis..."
              rows={3}
              disabled={loading}
            />
            {form.formState.errors.evidence && (
              <p className="text-sm text-red-600">{form.formState.errors.evidence.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conseils */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-blue-900 mb-1">
                Conseils pour rédiger une observation
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Soyez factuel et objectif dans la description</li>
                <li>• Utilisez un langage professionnel et neutre</li>
                <li>• Référencez les exigences ou critères non respectés</li>
                <li>• Incluez les preuves et éléments observés</li>
                <li>• Choisissez la sévérité en fonction de l'impact</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => window.history.back()}
          disabled={loading}
        >
          Annuler
        </Button>
        
        <Button
          type="submit"
          disabled={loading}
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isEditing ? "Mettre à jour" : "Créer l'observation"}
        </Button>
      </div>
    </form>
  )
}
