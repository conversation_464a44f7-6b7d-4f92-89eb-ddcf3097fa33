import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ObservationService } from "@/lib/services/observation-service"
import { updateObservationSchema } from "@/lib/validations/observation"
import { hasPermission, UserRole } from "@/lib/validations/user"
import { z } from "zod"

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * GET /api/observations/[id] - Obtenir une observation par ID
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const observation = await ObservationService.getObservationById(params.id)

      if (!observation) {
        return NextResponse.json(
          { success: false, error: "Observation non trouvée" },
          { status: 404 }
        )
      }

      // TODO: Vérifier l'accès selon le rôle et l'organisation

      return NextResponse.json({
        success: true,
        data: observation
      })

    } catch (error) {
      console.error("Erreur lors de la récupération de l'observation:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * PUT /api/observations/[id] - Mettre à jour une observation
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'update')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'observation existe
      const existingObservation = await ObservationService.getObservationById(params.id)
      
      if (!existingObservation) {
        return NextResponse.json(
          { success: false, error: "Observation non trouvée" },
          { status: 404 }
        )
      }

      // TODO: Vérifier que l'utilisateur peut modifier cette observation

      const body = await req.json()
      const validatedData = updateObservationSchema.parse(body)

      const updatedObservation = await ObservationService.updateObservation(params.id, validatedData)

      return NextResponse.json({
        success: true,
        data: updatedObservation
      })

    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'observation:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * DELETE /api/observations/[id] - Supprimer une observation
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'delete')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'observation existe
      const existingObservation = await ObservationService.getObservationById(params.id)
      
      if (!existingObservation) {
        return NextResponse.json(
          { success: false, error: "Observation non trouvée" },
          { status: 404 }
        )
      }

      // TODO: Vérifier que l'utilisateur peut supprimer cette observation
      // TODO: Empêcher la suppression si des actions sont liées

      await ObservationService.deleteObservation(params.id)

      return NextResponse.json({
        success: true,
        message: "Observation supprimée avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de la suppression de l'observation:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
