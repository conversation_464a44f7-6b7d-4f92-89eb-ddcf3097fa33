"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { AuditForm } from "@/components/features/audits/audit-form"
import { useAuditActions } from "@/hooks/use-audit-actions"
import { useFormData } from "@/hooks/use-form-data"
import { UpdateAuditInput, AuditWithRelations } from "@/lib/validations/audit"
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"

interface EditAuditPageProps {
  params: {
    id: string
  }
}

export default function EditAuditPage({ params }: EditAuditPageProps) {
  const router = useRouter()
  const [audit, setAudit] = useState<AuditWithRelations | null>(null)

  const {
    loading: actionLoading,
    error: actionError,
    getAudit,
    updateAudit,
    clearError
  } = useAuditActions()

  const {
    organizations,
    users,
    loading: dataLoading,
    error: dataError
  } = useFormData()

  useEffect(() => {
    const fetchAudit = async () => {
      const auditData = await getAudit(params.id)
      if (auditData) {
        setAudit(auditData)
      }
    }

    fetchAudit()
  }, [params.id, getAudit])

  const handleSubmit = async (data: UpdateAuditInput) => {
    const updatedAudit = await updateAudit(params.id, data)
    if (updatedAudit) {
      router.push(`/audits/${updatedAudit.id}`)
    }
  }

  const loading = dataLoading || actionLoading || !audit

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" disabled>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Modifier l'audit</h1>
            <p className="text-gray-600">Chargement...</p>
          </div>
        </div>

        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (dataError || actionError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Modifier l'audit</h1>
            <p className="text-gray-600">Erreur lors du chargement</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dataError || actionError}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!audit) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Audit non trouvé
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>

        <div>
          <h1 className="text-3xl font-bold magneto-title">Modifier l'audit</h1>
          <p className="text-gray-600">
            {audit.title}
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <AuditForm
        audit={audit}
        organizations={organizations}
        users={users}
        onSubmit={handleSubmit}
        loading={actionLoading}
        error={actionError}
      />
    </div>
  )
}
