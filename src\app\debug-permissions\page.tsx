"use client"

import { useSimplePermissions } from "@/lib/auth/simple-auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function DebugPermissionsPage() {
  const permissions = useSimplePermissions()

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Debug Permissions</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* User Info */}
        <Card>
          <CardHeader>
            <CardTitle>Informations Utilisateur</CardTitle>
            <CardDescription>État actuel de l'utilisateur connecté</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>Rôle:</strong> 
              <Badge variant="outline" className="ml-2">
                {permissions.userRole || "Non connecté"}
              </Badge>
            </div>
            <div>
              <strong>Admin:</strong> 
              <Badge variant={permissions.isAdmin ? "default" : "secondary"} className="ml-2">
                {permissions.isAdmin ? "Oui" : "Non"}
              </Badge>
            </div>
            <div>
              <strong>Super Admin:</strong> 
              <Badge variant={permissions.isSuperAdmin ? "default" : "secondary"} className="ml-2">
                {permissions.isSuperAdmin ? "Oui" : "Non"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Permissions Audits */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions Audits</CardTitle>
            <CardDescription>Permissions pour le module Audits</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Gérer les audits:</span>
              <Badge variant={permissions.canManageAudits ? "default" : "secondary"}>
                {permissions.canManageAudits ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Créer des audits:</span>
              <Badge variant={permissions.canCreateAudits ? "default" : "secondary"}>
                {permissions.canCreateAudits ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Modifier des audits:</span>
              <Badge variant={permissions.canUpdateAudits ? "default" : "secondary"}>
                {permissions.canUpdateAudits ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Supprimer des audits:</span>
              <Badge variant={permissions.canDeleteAudits ? "default" : "secondary"}>
                {permissions.canDeleteAudits ? "✓" : "✗"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Permissions Observations */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions Observations</CardTitle>
            <CardDescription>Permissions pour le module Observations</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>Gérer les observations:</span>
              <Badge variant={permissions.canManageObservations ? "default" : "secondary"}>
                {permissions.canManageObservations ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Créer des observations:</span>
              <Badge variant={permissions.canCreateObservations ? "default" : "secondary"}>
                {permissions.canCreateObservations ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Modifier des observations:</span>
              <Badge variant={permissions.canUpdateObservations ? "default" : "secondary"}>
                {permissions.canUpdateObservations ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Supprimer des observations:</span>
              <Badge variant={permissions.canDeleteObservations ? "default" : "secondary"}>
                {permissions.canDeleteObservations ? "✓" : "✗"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Permissions Spécifiques */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions Spécifiques</CardTitle>
            <CardDescription>Test des permissions spécifiques</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>audits:read:</span>
              <Badge variant={permissions.checkPermission('audits:read') ? "default" : "secondary"}>
                {permissions.checkPermission('audits:read') ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>observations:read:</span>
              <Badge variant={permissions.checkPermission('observations:read') ? "default" : "secondary"}>
                {permissions.checkPermission('observations:read') ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>users:read:</span>
              <Badge variant={permissions.checkPermission('users:read') ? "default" : "secondary"}>
                {permissions.checkPermission('users:read') ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>reports:read:</span>
              <Badge variant={permissions.checkPermission('reports:read') ? "default" : "secondary"}>
                {permissions.checkPermission('reports:read') ? "✓" : "✗"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
