const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUser() {
  try {
    console.log('🔄 Creating test users...')

    // Hash the password
    const hashedPassword = await bcrypt.hash('password123', 12)

    // Test users to create
    const testUsers = [
      {
        email: '<EMAIL>',
        name: 'Administrateur Test',
        role: 'SUPER_ADMIN'
      },
      {
        email: '<EMAIL>',
        name: 'Gestionnaire Test',
        role: 'MANAGER'
      },
      {
        email: '<EMAIL>',
        name: 'Auditeur Test',
        role: 'AUDITOR'
      },
      {
        email: '<EMAIL>',
        name: 'Utilisateur Test',
        role: 'USER'
      }
    ]

    for (const userData of testUsers) {
      try {
        const user = await prisma.user.create({
          data: {
            ...userData,
            password: hashedPassword,
            isActive: true,
            emailVerified: true,
          }
        })

        console.log(`✅ User created: ${userData.email} (${userData.role})`)
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`ℹ️ User already exists: ${userData.email}`)
        } else {
          console.error(`❌ Error creating user ${userData.email}:`, error.message)
        }
      }
    }

    console.log('\n📋 Test Users Summary:')
    console.log('📧 All emails use password: password123')
    console.log('👤 <EMAIL> (SUPER_ADMIN)')
    console.log('👤 <EMAIL> (MANAGER)')
    console.log('👤 <EMAIL> (AUDITOR)')
    console.log('👤 <EMAIL> (USER)')

  } catch (error) {
    console.error('❌ Error in createTestUser:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
