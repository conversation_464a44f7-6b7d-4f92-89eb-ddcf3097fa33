import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

// Mock data pour les organisations (en attendant l'implémentation complète)
const mockOrganizations = [
  {
    id: "org_1",
    name: "Organisation Principale",
    description: "Organisation principale du système",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "org_2", 
    name: "Filiale Nord",
    description: "Filiale située dans le nord",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "org_3",
    name: "Filiale Sud", 
    description: "Filiale située dans le sud",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
]

/**
 * GET /api/organizations - Obtenir la liste des organisations
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'organizations', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Si l'utilisateur n'est pas admin, ne retourner que son organisation
      let organizations = mockOrganizations
      if (user.role !== "SUPER_ADMIN" && user.role !== "ADMIN" && user.organizationId) {
        organizations = mockOrganizations.filter(org => org.id === user.organizationId)
      }

      return NextResponse.json({
        success: true,
        data: organizations
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des organisations:", error)
      
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * POST /api/organizations - Créer une nouvelle organisation
 */
export async function POST(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'organizations', 'create')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const body = await req.json()
      
      // Validation basique
      if (!body.name || body.name.trim().length === 0) {
        return NextResponse.json(
          { success: false, error: "Le nom de l'organisation est requis" },
          { status: 400 }
        )
      }

      // Créer une nouvelle organisation (mock)
      const newOrganization = {
        id: `org_${Date.now()}`,
        name: body.name.trim(),
        description: body.description || "",
        isActive: body.isActive !== undefined ? body.isActive : true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Ajouter à la liste mock (en production, sauvegarder en base)
      mockOrganizations.push(newOrganization)

      return NextResponse.json({
        success: true,
        data: newOrganization,
        message: "Organisation créée avec succès"
      }, { status: 201 })

    } catch (error) {
      console.error("Erreur lors de la création de l'organisation:", error)
      
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
