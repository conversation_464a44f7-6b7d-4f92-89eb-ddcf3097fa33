"use client"

import { useSession } from "@/lib/auth/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function AuthDebug() {
  const { data: session, error, isPending } = useSession()

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>État de l'authentification</CardTitle>
        <CardDescription>Informations de débogage pour Better Auth</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500">État</label>
            <div className="mt-1">
              {isPending && <Badge variant="outline">Chargement...</Badge>}
              {error && <Badge variant="destructive">Erreur</Badge>}
              {session && <Badge variant="default">Connecté</Badge>}
              {!isPending && !error && !session && <Badge variant="secondary">Non connecté</Badge>}
            </div>
          </div>
          
          {session?.user && (
            <div>
              <label className="text-sm font-medium text-gray-500">Utilisateur</label>
              <p className="text-sm">{session.user.email}</p>
            </div>
          )}
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 font-medium">Erreur d'authentification</p>
            <p className="text-red-500 text-sm mt-1">{error.message}</p>
          </div>
        )}

        <details className="mt-4">
          <summary className="cursor-pointer text-sm font-medium">Données brutes</summary>
          <pre className="mt-2 p-3 bg-gray-50 rounded-lg text-xs overflow-auto">
            {JSON.stringify({ session, error, isPending }, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  )
}
