"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useSession } from "@/lib/auth/client"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, XCircle, Loader2 } from "lucide-react"

export default function TestPermissionsPage() {
  const { data: session } = useSession()
  const [testResults, setTestResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  const testAPIs = async () => {
    setLoading(true)
    setTestResults([])

    const apis = [
      { name: "Organizations", url: "/api/organizations" },
      { name: "Users", url: "/api/users" },
      { name: "<PERSON><PERSON>", url: "/api/audits" }
    ]

    const results = []

    for (const api of apis) {
      try {
        const response = await fetch(api.url)
        const data = await response.json()
        
        results.push({
          name: api.name,
          url: api.url,
          status: response.status,
          success: response.ok,
          message: data.error || "Succès",
          data: response.ok ? data : null
        })
      } catch (error: any) {
        results.push({
          name: api.name,
          url: api.url,
          status: 0,
          success: false,
          message: error.message,
          data: null
        })
      }
    }

    setTestResults(results)
    setLoading(false)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Test des permissions</h1>
        <p className="text-gray-600">
          Testez l'accès aux APIs avec votre rôle actuel
        </p>
      </div>

      {/* Informations utilisateur */}
      <Card>
        <CardHeader>
          <CardTitle>Utilisateur connecté</CardTitle>
        </CardHeader>
        <CardContent>
          {session?.user ? (
            <div className="space-y-2">
              <p><strong>Email:</strong> {session.user.email}</p>
              <p><strong>Nom:</strong> {session.user.name || "Non défini"}</p>
              <p><strong>Rôle:</strong> <Badge variant="outline">{session.user.role}</Badge></p>
              <p><strong>ID:</strong> {session.user.id}</p>
            </div>
          ) : (
            <p>Aucun utilisateur connecté</p>
          )}
        </CardContent>
      </Card>

      {/* Test des APIs */}
      <Card>
        <CardHeader>
          <CardTitle>Test d'accès aux APIs</CardTitle>
          <CardDescription>
            Cliquez pour tester l'accès aux différentes APIs avec votre rôle actuel
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testAPIs} 
            disabled={loading || !session?.user}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Test en cours...
              </>
            ) : (
              "Tester les APIs"
            )}
          </Button>

          {/* Résultats */}
          {testResults.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-semibold">Résultats des tests :</h3>
              {testResults.map((result, index) => (
                <Alert key={index} className={result.success ? "border-green-200" : "border-red-200"}>
                  <div className="flex items-center gap-2">
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <strong>{result.name}</strong>
                        <Badge variant={result.success ? "default" : "destructive"}>
                          {result.status}
                        </Badge>
                      </div>
                      <AlertDescription className="mt-1">
                        {result.message}
                      </AlertDescription>
                      {result.success && result.data && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm text-gray-600">
                            Voir les données
                          </summary>
                          <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
