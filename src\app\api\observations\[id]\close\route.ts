import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ObservationService } from "@/lib/services/observation-service"
import { hasPermission, UserRole } from "@/lib/validations/user"

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * POST /api/observations/[id]/close - Marquer une observation comme fermée
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions (seuls les managers et admins peuvent fermer)
      if (!hasPermission(user.role as UserRole, 'observations', 'update') || 
          (user.role !== UserRole.MANAGER && user.role !== UserRole.ADMIN && user.role !== UserRole.SUPER_ADMIN)) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes pour fermer une observation" },
          { status: 403 }
        )
      }

      // Vérifier que l'observation existe
      const existingObservation = await ObservationService.getObservationById(params.id)
      
      if (!existingObservation) {
        return NextResponse.json(
          { success: false, error: "Observation non trouvée" },
          { status: 404 }
        )
      }

      // Vérifier que l'observation peut être fermée (doit être résolue)
      if (existingObservation.status !== 'RESOLVED') {
        return NextResponse.json(
          { success: false, error: "Seules les observations résolues peuvent être fermées" },
          { status: 400 }
        )
      }

      const closedObservation = await ObservationService.closeObservation(params.id)

      return NextResponse.json({
        success: true,
        data: closedObservation,
        message: "Observation fermée avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de la fermeture de l'observation:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
