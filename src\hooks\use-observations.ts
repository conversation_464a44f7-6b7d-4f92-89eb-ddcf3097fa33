"use client"

import { useState, useEffect, useCallback } from "react"
import { ObservationFilters, ObservationWithRelations } from "@/lib/validations/observation"

interface UseObservationsResult {
  observations: ObservationWithRelations[]
  total: number
  totalPages: number
  loading: boolean
  error: string | null
  filters: ObservationFilters
  setFilters: (filters: ObservationFilters) => void
  refetch: () => Promise<void>
}

const defaultFilters: ObservationFilters = {
  page: 1,
  limit: 10,
  sortBy: "createdAt",
  sortOrder: "desc"
}

export function useObservations(initialFilters?: Partial<ObservationFilters>): UseObservationsResult {
  const [observations, setObservations] = useState<ObservationWithRelations[]>([])
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<ObservationFilters>({
    ...defaultFilters,
    ...initialFilters
  })

  const fetchObservations = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Construire les paramètres de requête
      const searchParams = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams.append(key, value.toString())
        }
      })

      const response = await fetch(`/api/observations?${searchParams.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erreur lors du chargement des observations")
      }

      const data = await response.json()
      
      if (data.success) {
        setObservations(data.data.observations)
        setTotal(data.data.total)
        setTotalPages(data.data.totalPages)
      } else {
        throw new Error(data.error || "Erreur lors du chargement des observations")
      }
    } catch (err) {
      console.error("Erreur lors du chargement des observations:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
      setObservations([])
      setTotal(0)
      setTotalPages(0)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Charger les observations quand les filtres changent
  useEffect(() => {
    fetchObservations()
  }, [fetchObservations])

  const updateFilters = useCallback((newFilters: ObservationFilters) => {
    setFilters(newFilters)
  }, [])

  return {
    observations,
    total,
    totalPages,
    loading,
    error,
    filters,
    setFilters: updateFilters,
    refetch: fetchObservations
  }
}
