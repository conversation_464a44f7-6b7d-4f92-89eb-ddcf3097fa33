const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyUsers() {
  try {
    console.log('🔍 Verifying users in database...')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        emailVerified: true,
        createdAt: true,
        password: true // Just to check if it exists
      },
      orderBy: {
        email: 'asc'
      }
    })
    
    console.log(`\n📊 Found ${users.length} users in database:`)
    console.log('=' .repeat(80))
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email}`)
      console.log(`   👤 Name: ${user.name || 'N/A'}`)
      console.log(`   🎭 Role: ${user.role}`)
      console.log(`   ✅ Active: ${user.isActive}`)
      console.log(`   📧 Email Verified: ${user.emailVerified}`)
      console.log(`   🔑 Has Password: ${user.password ? 'Yes' : 'No'}`)
      console.log(`   📅 Created: ${user.createdAt.toISOString()}`)
      console.log(`   🆔 ID: ${user.id}`)
      console.log('')
    })
    
    // Test authentication for each user
    console.log('🔐 Testing authentication for each user...')
    console.log('=' .repeat(80))
    
    for (const user of users) {
      try {
        const response = await fetch('http://localhost:8080/api/auth/sign-in/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: user.email,
            password: 'password123'
          })
        })
        
        if (response.ok) {
          const result = await response.json()
          console.log(`✅ ${user.email}: Authentication successful`)
        } else {
          const error = await response.text()
          console.log(`❌ ${user.email}: Authentication failed - ${error}`)
        }
      } catch (error) {
        console.log(`❌ ${user.email}: Authentication error - ${error.message}`)
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
  } catch (error) {
    console.error('❌ Error verifying users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifyUsers()
