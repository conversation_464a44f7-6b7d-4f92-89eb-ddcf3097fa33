"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { authClient } from "@/lib/auth/client"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { AlertCircle, CheckCircle } from "lucide-react"

export default function TestBetterAuthPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("password123")
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  
  const { data: session, error: sessionError } = authClient.useSession()
  const permissions = usePermissions()

  const testSession = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/session")
      const data = await response.json()
      setResult({ type: "session", data })
    } catch (error: any) {
      setResult({ type: "session", error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testSignIn = async () => {
    try {
      setLoading(true)
      const result = await authClient.signIn.email({
        email,
        password
      })
      setResult({ type: "signIn", data: result })
    } catch (error: any) {
      setResult({ type: "signIn", error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testSignUp = async () => {
    try {
      setLoading(true)
      const result = await authClient.signUp.email({
        email,
        password,
        name: "Test User"
      })
      setResult({ type: "signUp", data: result })
    } catch (error: any) {
      setResult({ type: "signUp", error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testSignOut = async () => {
    try {
      setLoading(true)
      await authClient.signOut()
      setResult({ type: "signOut", data: "Déconnexion réussie" })
    } catch (error: any) {
      setResult({ type: "signOut", error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Test Better Auth</h1>
        <p className="text-gray-600">
          Test de la configuration Better Auth corrigée
        </p>
      </div>

      {/* Session Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {session ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600" />
            )}
            État de la session
          </CardTitle>
        </CardHeader>
        <CardContent>
          {sessionError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>Erreur de session: {sessionError.message}</AlertDescription>
            </Alert>
          )}
          
          {session ? (
            <div className="space-y-2">
              <Badge variant="default">Connecté</Badge>
              <div className="text-sm">
                <p><strong>Email:</strong> {session.user.email}</p>
                <p><strong>Nom:</strong> {session.user.name || "Non renseigné"}</p>
                <p><strong>Rôle:</strong> {session.user.role}</p>
              </div>
            </div>
          ) : (
            <Badge variant="secondary">Non connecté</Badge>
          )}
        </CardContent>
      </Card>

      {/* Test Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Tests d'authentification</CardTitle>
            <CardDescription>Testez les fonctions Better Auth</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="magneto-input"
              />
            </div>
            <div>
              <Label htmlFor="password">Mot de passe</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="magneto-input"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button 
                onClick={testSession} 
                disabled={loading}
                variant="outline"
              >
                Test Session
              </Button>
              <Button 
                onClick={testSignIn} 
                disabled={loading}
                className="magneto-button"
              >
                Se connecter
              </Button>
              <Button 
                onClick={testSignUp} 
                disabled={loading}
                variant="outline"
              >
                S'inscrire
              </Button>
              {session && (
                <Button 
                  onClick={testSignOut} 
                  disabled={loading}
                  variant="destructive"
                >
                  Se déconnecter
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions</CardTitle>
            <CardDescription>État des permissions utilisateur</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span>Gérer utilisateurs:</span>
                <Badge variant={permissions.canManageUsers ? "default" : "secondary"}>
                  {permissions.canManageUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Créer utilisateurs:</span>
                <Badge variant={permissions.canCreateUsers ? "default" : "secondary"}>
                  {permissions.canCreateUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Modifier utilisateurs:</span>
                <Badge variant={permissions.canUpdateUsers ? "default" : "secondary"}>
                  {permissions.canUpdateUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Supprimer utilisateurs:</span>
                <Badge variant={permissions.canDeleteUsers ? "default" : "secondary"}>
                  {permissions.canDeleteUsers ? "✓" : "✗"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Results */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle>Résultat du test: {result.type}</CardTitle>
          </CardHeader>
          <CardContent>
            {result.error ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{result.error}</AlertDescription>
              </Alert>
            ) : (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>Test réussi</AlertDescription>
              </Alert>
            )}
            <pre className="mt-4 bg-gray-50 p-4 rounded-lg text-xs overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
