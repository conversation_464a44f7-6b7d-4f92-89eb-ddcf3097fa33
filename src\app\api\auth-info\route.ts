import { NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"

export async function GET() {
  try {
    // Essayons de voir quelles méthodes sont disponibles sur l'objet auth
    const authInfo = {
      hasHandler: !!auth.handler,
      hasApi: !!auth.api,
      availableMethods: Object.keys(auth.handler || {}),
      apiMethods: auth.api ? Object.keys(auth.api) : [],
      // Essayons d'obtenir des informations sur la configuration
      config: {
        hasDatabase: !!auth.options?.database,
        hasEmailPassword: !!auth.options?.emailAndPassword,
        basePath: auth.options?.basePath || '/api/auth',
      }
    }

    return NextResponse.json({
      success: true,
      data: authInfo,
      message: "Informations sur Better Auth"
    })
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
