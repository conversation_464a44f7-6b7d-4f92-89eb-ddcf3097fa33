"use client"

import { useState } from "react"
import { authClient } from "@/lib/auth/client"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function AuthTestPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("password123")
  const [result, setResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const testSignIn = async () => {
    setIsLoading(true)
    setResult(null)
    
    try {
      console.log("Testing sign in with:", { email, password })
      
      const result = await authClient.signIn.email({
        email,
        password,
      })
      
      console.log("Sign in result:", result)
      setResult(result)
      
    } catch (error) {
      console.error("Sign in error:", error)
      setResult({ error: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  const testGetSession = async () => {
    setIsLoading(true)
    
    try {
      const session = await authClient.getSession()
      console.log("Current session:", session)
      setResult(session)
    } catch (error) {
      console.error("Get session error:", error)
      setResult({ error: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  const testSignOut = async () => {
    setIsLoading(true)
    
    try {
      await authClient.signOut()
      setResult({ message: "Signed out successfully" })
    } catch (error) {
      console.error("Sign out error:", error)
      setResult({ error: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Better Auth Test</CardTitle>
          <CardDescription>
            Test the Better Auth authentication system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Email:</label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Email"
            />
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Password:</label>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Password"
            />
          </div>
          
          <div className="flex gap-2 flex-wrap">
            <Button 
              onClick={testSignIn} 
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Test Sign In
            </Button>
            <Button 
              onClick={testGetSession} 
              disabled={isLoading}
              variant="outline"
            >
              Get Session
            </Button>
            <Button 
              onClick={testSignOut} 
              disabled={isLoading}
              variant="destructive"
            >
              Sign Out
            </Button>
          </div>
          
          {result && (
            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-2">Result:</h3>
              <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
      
      <div className="mt-6 p-4 bg-blue-50 rounded-md">
        <h3 className="font-semibold mb-2">Test Users:</h3>
        <ul className="text-sm space-y-1">
          <li>• <EMAIL> (SUPER_ADMIN)</li>
          <li>• <EMAIL> (MANAGER)</li>
          <li>• <EMAIL> (AUDITOR)</li>
          <li>• <EMAIL> (USER)</li>
          <li className="font-medium">Password for all: password123</li>
        </ul>
      </div>
    </div>
  )
}
