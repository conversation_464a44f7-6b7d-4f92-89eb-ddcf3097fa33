"use client"

import { useState } from "react"
import { CreateAuditInput, UpdateAuditInput, AuditWithRelations } from "@/lib/validations/audit"

interface UseAuditActionsResult {
  loading: boolean
  error: string | null
  createAudit: (data: CreateAuditInput) => Promise<AuditWithRelations | null>
  updateAudit: (id: string, data: UpdateAuditInput) => Promise<AuditWithRelations | null>
  deleteAudit: (id: string) => Promise<boolean>
  getAudit: (id: string) => Promise<AuditWithRelations | null>
  clearError: () => void
}

export function useAuditActions(): UseAuditActionsResult {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearError = () => setError(null)

  const createAudit = async (data: CreateAuditInput): Promise<AuditWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/audits", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la création de l'audit")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la création de l'audit")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la création de l'audit:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const updateAudit = async (id: string, data: UpdateAuditInput): Promise<AuditWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/audits/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la mise à jour de l'audit")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la mise à jour de l'audit")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la mise à jour de l'audit:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const deleteAudit = async (id: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/audits/${id}`, {
        method: "DELETE",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la suppression de l'audit")
      }

      return result.success
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la suppression de l'audit:", err)
      return false
    } finally {
      setLoading(false)
    }
  }

  const getAudit = async (id: string): Promise<AuditWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/audits/${id}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la récupération de l'audit")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la récupération de l'audit")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la récupération de l'audit:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    error,
    createAudit,
    updateAudit,
    deleteAudit,
    getAudit,
    clearError
  }
}
