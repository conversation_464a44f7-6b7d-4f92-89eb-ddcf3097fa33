import { Badge } from "@/components/ui/badge"
import { 
  ObservationStatusType, 
  getObservationStatusLabel, 
  getObservationStatusColor 
} from "@/lib/validations/observation"

interface ObservationStatusBadgeProps {
  status: ObservationStatusType
  className?: string
}

export function ObservationStatusBadge({ status, className }: ObservationStatusBadgeProps) {
  return (
    <Badge 
      variant="outline" 
      className={`${getObservationStatusColor(status)} ${className}`}
    >
      {getObservationStatusLabel(status)}
    </Badge>
  )
}
