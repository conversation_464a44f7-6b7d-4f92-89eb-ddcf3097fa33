"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { ObservationForm } from "@/components/features/observations"
import { useObservationActions } from "@/hooks/use-observation-actions"
import { useFormData } from "@/hooks/use-form-data"
import { CreateObservationInput } from "@/lib/validations/observation"
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react"
import { useRouter, useSearchParams } from "next/navigation"

export default function NewObservationPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const defaultAuditId = searchParams.get('auditId')

  const {
    loading: actionLoading,
    error: actionError,
    createObservation,
    clearError
  } = useObservationActions()

  const {
    audits,
    loading: dataLoading,
    error: dataError
  } = useFormData()

  const handleSubmit = async (data: CreateObservationInput) => {
    const observation = await createObservation(data)
    if (observation) {
      router.push(`/observations/${observation.id}`)
    }
  }

  if (dataLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" disabled>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Nouvelle observation</h1>
            <p className="text-gray-600">Chargement...</p>
          </div>
        </div>

        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (dataError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Nouvelle observation</h1>
            <p className="text-gray-600">Erreur lors du chargement</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{dataError}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>

        <div>
          <h1 className="text-3xl font-bold magneto-title">Nouvelle observation</h1>
          <p className="text-gray-600">
            Créez une nouvelle observation d'audit
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <ObservationForm
        audits={audits}
        onSubmit={handleSubmit}
        loading={actionLoading}
        error={actionError}
        defaultAuditId={defaultAuditId || undefined}
      />
    </div>
  )
}
