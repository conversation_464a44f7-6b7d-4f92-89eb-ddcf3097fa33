import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ObservationService } from "@/lib/services/observation-service"
import { createObservationSchema, observationFiltersSchema } from "@/lib/validations/observation"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * GET /api/observations - Obtenir la liste des observations
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Parser les paramètres de requête
      const { searchParams } = new URL(req.url)
      const filters = observationFiltersSchema.parse({
        auditId: searchParams.get('auditId') || undefined,
        severity: searchParams.get('severity') || undefined,
        status: searchParams.get('status') || undefined,
        search: searchParams.get('search') || undefined,
        page: searchParams.get('page') || 1,
        limit: searchParams.get('limit') || 10,
        sortBy: searchParams.get('sortBy') || 'createdAt',
        sortOrder: searchParams.get('sortOrder') || 'desc'
      })

      const result = await ObservationService.getObservations(filters)

      return NextResponse.json({
        success: true,
        data: result
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des observations:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Paramètres invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * POST /api/observations - Créer une nouvelle observation
 */
export async function POST(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'create')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = createObservationSchema.parse(body)

      // Vérifier que l'utilisateur a accès à l'audit
      // TODO: Ajouter la vérification d'accès à l'audit spécifique

      const observation = await ObservationService.createObservation(validatedData)

      return NextResponse.json({
        success: true,
        data: observation
      }, { status: 201 })

    } catch (error) {
      console.error("Erreur lors de la création de l'observation:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
