import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"

export async function GET(request: NextRequest) {
  try {
    // Testons différentes méthodes pour obtenir la session
    const results: any = {
      timestamp: new Date().toISOString(),
      tests: []
    }

    // Test 1: Vérifier si auth.api existe
    if (auth.api) {
      results.tests.push({
        name: "auth.api exists",
        success: true,
        methods: Object.keys(auth.api)
      })

      // Test 2: Essayer getSession si elle existe
      if (typeof auth.api.getSession === 'function') {
        try {
          const session = await auth.api.getSession({
            headers: request.headers
          })
          results.tests.push({
            name: "auth.api.getSession",
            success: true,
            data: session
          })
        } catch (error: any) {
          results.tests.push({
            name: "auth.api.getSession",
            success: false,
            error: error.message
          })
        }
      } else {
        results.tests.push({
          name: "auth.api.getSession",
          success: false,
          error: "Method not found"
        })
      }
    } else {
      results.tests.push({
        name: "auth.api exists",
        success: false,
        error: "auth.api is undefined"
      })
    }

    // Test 3: Vérifier les headers de la requête
    const headers: any = {}
    request.headers.forEach((value, key) => {
      headers[key] = value
    })

    results.headers = headers
    results.cookies = request.cookies.getAll()

    return NextResponse.json({
      success: true,
      data: results
    })
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Test de connexion avec Better Auth
    if (auth.api && typeof auth.api.signInEmail === 'function') {
      try {
        const result = await auth.api.signInEmail({
          email: body.email || "<EMAIL>",
          password: body.password || "password123",
          headers: request.headers
        })
        
        return NextResponse.json({
          success: true,
          data: result,
          message: "Connexion testée avec succès"
        })
      } catch (error: any) {
        return NextResponse.json({
          success: false,
          error: error.message,
          details: "Erreur lors de la connexion"
        }, { status: 400 })
      }
    } else {
      return NextResponse.json({
        success: false,
        error: "auth.api.signInEmail not available"
      }, { status: 500 })
    }
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
