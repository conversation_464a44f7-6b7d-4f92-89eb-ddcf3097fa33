"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useSession } from "@/lib/auth/client"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { ROLE_LABELS } from "@/lib/validations/user"

export default function TestAuthPage() {
  const { data: session, error, isPending } = useSession()
  const permissions = usePermissions()

  if (isPending) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold magneto-title">Test d'authentification</h1>
        <Card>
          <CardContent className="pt-6">
            <p>Chargement de la session...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Test d'authentification</h1>
        <p className="text-gray-600">
          Vérification de l'état de l'authentification et des permissions
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Session Info */}
        <Card>
          <CardHeader>
            <CardTitle>Informations de session</CardTitle>
            <CardDescription>État actuel de l'authentification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 font-medium">Erreur d'authentification</p>
                <p className="text-red-500 text-sm">{error.message}</p>
              </div>
            )}
            
            {session?.user ? (
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Utilisateur connecté</label>
                  <p className="text-lg font-semibold text-green-600">✓ Oui</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Nom</label>
                  <p className="text-lg">{session.user.name || "Non renseigné"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-lg">{session.user.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Rôle</label>
                  <div className="mt-1">
                    <Badge variant="outline">
                      {ROLE_LABELS[session.user.role as keyof typeof ROLE_LABELS] || session.user.role}
                    </Badge>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <label className="text-sm font-medium text-gray-500">Utilisateur connecté</label>
                <p className="text-lg font-semibold text-red-600">✗ Non</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions</CardTitle>
            <CardDescription>Permissions accordées à l'utilisateur actuel</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="flex justify-between">
                <span>Gérer utilisateurs:</span>
                <Badge variant={permissions.canManageUsers ? "default" : "secondary"}>
                  {permissions.canManageUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Créer utilisateurs:</span>
                <Badge variant={permissions.canCreateUsers ? "default" : "secondary"}>
                  {permissions.canCreateUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Modifier utilisateurs:</span>
                <Badge variant={permissions.canUpdateUsers ? "default" : "secondary"}>
                  {permissions.canUpdateUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Supprimer utilisateurs:</span>
                <Badge variant={permissions.canDeleteUsers ? "default" : "secondary"}>
                  {permissions.canDeleteUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Gérer audits:</span>
                <Badge variant={permissions.canManageAudits ? "default" : "secondary"}>
                  {permissions.canManageAudits ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Gérer rapports:</span>
                <Badge variant={permissions.canManageReports ? "default" : "secondary"}>
                  {permissions.canManageReports ? "✓" : "✗"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de débogage</CardTitle>
          <CardDescription>Données brutes pour le débogage</CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-50 p-4 rounded-lg text-xs overflow-auto">
            {JSON.stringify({ session, error, permissions }, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}
