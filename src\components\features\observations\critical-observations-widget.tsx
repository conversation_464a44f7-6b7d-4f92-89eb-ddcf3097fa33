"use client"

import { useEffect, useState } from "react"
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ObservationSeverityBadge, ObservationStatusBadge } from "@/components/features/observations"
import { ObservationWithRelations } from "@/lib/validations/observation"
import { AlertTriangle, Eye, ArrowRight } from "lucide-react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface CriticalObservationsWidgetProps {
  limit?: number
  auditId?: string
}

export function CriticalObservationsWidget({ 
  limit = 5, 
  auditId 
}: CriticalObservationsWidgetProps) {
  const router = useRouter()
  const [observations, setObservations] = useState<ObservationWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCriticalObservations = async () => {
      try {
        setLoading(true)
        setError(null)

        const searchParams = new URLSearchParams({
          severity: 'CRITICAL',
          status: 'OPEN',
          limit: limit.toString(),
          sortBy: 'createdAt',
          sortOrder: 'desc'
        })

        if (auditId) {
          searchParams.append('auditId', auditId)
        }

        const response = await fetch(`/api/observations?${searchParams.toString()}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Erreur lors du chargement")
        }

        const data = await response.json()
        
        if (data.success) {
          setObservations(data.data.observations)
        } else {
          throw new Error(data.error || "Erreur lors du chargement")
        }
      } catch (err) {
        console.error("Erreur lors du chargement des observations critiques:", err)
        setError(err instanceof Error ? err.message : "Erreur inconnue")
      } finally {
        setLoading(false)
      }
    }

    fetchCriticalObservations()
  }, [limit, auditId])

  const formatDate = (date: Date) => {
    return format(date, "dd MMM", { locale: fr })
  }

  const handleViewObservation = (observation: ObservationWithRelations) => {
    router.push(`/observations/${observation.id}`)
  }

  const handleViewAll = () => {
    const params = new URLSearchParams({
      severity: 'CRITICAL',
      status: 'OPEN'
    })
    
    if (auditId) {
      params.append('auditId', auditId)
    }
    
    router.push(`/observations?${params.toString()}`)
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Observations critiques
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Observations critiques
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <AlertTriangle className="mx-auto h-8 w-8 text-red-400 mb-2" />
            <p className="text-sm text-gray-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Observations critiques
            {observations.length > 0 && (
              <Badge variant="destructive" className="ml-2">
                {observations.length}
              </Badge>
            )}
          </CardTitle>
          
          {observations.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewAll}
              className="text-red-600 hover:text-red-700"
            >
              Voir tout
              <ArrowRight className="ml-1 h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {observations.length === 0 ? (
          <div className="text-center py-6">
            <AlertTriangle className="mx-auto h-8 w-8 text-green-400 mb-2" />
            <h3 className="text-sm font-medium text-gray-900">Aucune observation critique</h3>
            <p className="text-sm text-gray-500">
              Excellente nouvelle ! Aucune observation critique ouverte.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {observations.map((observation) => (
              <div
                key={observation.id}
                className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleViewObservation(observation)}
              >
                <div className="flex-shrink-0 mt-1">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900 line-clamp-1">
                        {observation.title}
                      </h4>
                      <p className="text-xs text-gray-600 line-clamp-2 mt-1">
                        {observation.description}
                      </p>
                    </div>
                    
                    <div className="flex flex-col items-end gap-1">
                      <span className="text-xs text-gray-500">
                        {formatDate(observation.createdAt)}
                      </span>
                      <ObservationStatusBadge status={observation.status} />
                    </div>
                  </div>
                  
                  {!auditId && (
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs">
                        {observation.audit.title}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
