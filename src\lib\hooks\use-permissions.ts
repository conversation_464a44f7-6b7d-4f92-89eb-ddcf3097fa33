"use client"

import { authClient } from "@/lib/auth/client"
import { hasPermission, UserRole } from "@/lib/validations/user"

export function usePermissions() {
  const { data: session, error } = authClient.useSession()
  const userRole = session?.user?.role as UserRole

  // Si il y a une erreur ou pas de session, on retourne des permissions par défaut
  if (error || !session?.user) {
    return {
      userRole: undefined,
      checkPermission: () => false,
      canManageUsers: false,
      canCreateUsers: false,
      canUpdateUsers: false,
      canDeleteUsers: false,
      canManageAudits: false,
      canCreateAudits: false,
      canUpdateAudits: false,
      canDeleteAudits: false,
      canManageReports: false,
      canCreateReports: false,
      canUpdateReports: false,
      canDeleteReports: false,
      isAdmin: false,
      isSuperAdmin: false,
      isManager: false,
      isAuditor: false,
    }
  }

  const checkPermission = (permission: string): boolean => {
    if (!userRole) return false
    const [resource, action] = permission.split(':')
    return hasPermission(userRole, resource, action)
  }

  const canManageUsers = checkPermission("users:read")
  const canCreateUsers = checkPermission("users:create")
  const canUpdateUsers = checkPermission("users:update")
  const canDeleteUsers = checkPermission("users:delete")
  
  const canManageAudits = checkPermission("audits:read")
  const canCreateAudits = checkPermission("audits:create")
  const canUpdateAudits = checkPermission("audits:update")
  const canDeleteAudits = checkPermission("audits:delete")
  
  const canManageReports = checkPermission("reports:read")
  const canCreateReports = checkPermission("reports:create")
  const canUpdateReports = checkPermission("reports:update")
  const canDeleteReports = checkPermission("reports:delete")

  const isAdmin = userRole === "SUPER_ADMIN" || userRole === "ADMIN"
  const isSuperAdmin = userRole === "SUPER_ADMIN"
  const isManager = userRole === "MANAGER"
  const isAuditor = userRole === "AUDITOR"

  return {
    userRole,
    checkPermission,
    
    // User permissions
    canManageUsers,
    canCreateUsers,
    canUpdateUsers,
    canDeleteUsers,
    
    // Audit permissions
    canManageAudits,
    canCreateAudits,
    canUpdateAudits,
    canDeleteAudits,
    
    // Report permissions
    canManageReports,
    canCreateReports,
    canUpdateReports,
    canDeleteReports,
    
    // Role checks
    isAdmin,
    isSuperAdmin,
    isManager,
    isAuditor,
  }
}
