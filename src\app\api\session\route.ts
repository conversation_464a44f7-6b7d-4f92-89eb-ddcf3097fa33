import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"

export async function GET(request: NextRequest) {
  try {
    // Utilisons la bonne méthode Better Auth pour récupérer la session
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session) {
      return NextResponse.json({
        success: false,
        authenticated: false,
        message: "Aucune session active"
      }, { status: 401 })
    }

    return NextResponse.json({
      success: true,
      authenticated: true,
      user: session.user,
      session: {
        id: session.session.id,
        expiresAt: session.session.expiresAt,
        createdAt: session.session.createdAt,
        updatedAt: session.session.updatedAt
      }
    })
  } catch (error: any) {
    console.error("Session API error:", error)
    return NextResponse.json({
      success: false,
      authenticated: false,
      error: error.message || "Erreur lors de la récupération de la session"
    }, { status: 500 })
  }
}
