"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useSimpleAuth } from "@/lib/auth/simple-auth"
import { Eye, EyeOff, AlertCircle } from "lucide-react"

export default function SimpleLoginPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("password123")
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const { signIn, isLoading } = useSimpleAuth()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    try {
      await signIn(email, password)
      router.push("/dashboard")
    } catch (err: any) {
      setError(err.message || "Erreur lors de la connexion")
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold magneto-title">Magneto</h1>
          <p className="mt-2 text-gray-600">Système de gestion d'audits</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Connexion Simple</CardTitle>
            <CardDescription>
              Connectez-vous avec vos identifiants de test
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div>
                <Label htmlFor="email">Adresse email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="magneto-input"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <Label htmlFor="password">Mot de passe</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="magneto-input pr-10"
                    placeholder="Votre mot de passe"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className="w-full magneto-button"
              >
                {isLoading ? "Connexion..." : "Se connecter"}
              </Button>
            </form>

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Comptes de test :</h3>
              <div className="text-sm text-blue-700 space-y-1">
                <div><strong>Super Admin:</strong> <EMAIL></div>
                <div><strong>Manager:</strong> <EMAIL></div>
                <div><strong>Auditeur:</strong> <EMAIL></div>
                <div><strong>Utilisateur:</strong> <EMAIL></div>
                <div className="mt-2"><strong>Mot de passe:</strong> password123</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
