import { z } from "zod"

// Enum pour les statuts d'audit
export const AuditStatus = {
  PLANNED: "PLANNED",
  IN_PROGRESS: "IN_PROGRESS", 
  COMPLETED: "COMPLETED",
  CANCELLED: "CANCELLED",
  ON_HOLD: "ON_HOLD"
} as const

export type AuditStatusType = typeof AuditStatus[keyof typeof AuditStatus]

// Enum pour les rôles dans un audit
export const AuditUserRole = {
  LEAD_AUDITOR: "LEAD_AUDITOR",
  AUDITOR: "AUDITOR",
  OBSERVER: "OBSERVER"
} as const

export type AuditUserRoleType = typeof AuditUserRole[keyof typeof AuditUserRole]

// Schema de validation pour la création d'un audit
export const createAuditSchema = z.object({
  title: z.string().min(1, "Le titre est requis").max(200, "Le titre ne peut pas dépasser 200 caractères"),
  description: z.string().optional(),
  startDate: z.string().datetime("Date de début invalide"),
  endDate: z.string().datetime("Date de fin invalide").optional(),
  organizationId: z.string().min(1, "L'organisation est requise"),
  auditorIds: z.array(z.string()).min(1, "Au moins un auditeur est requis"),
  leadAuditorId: z.string().min(1, "Un auditeur principal est requis")
}).refine((data) => {
  if (data.endDate) {
    return new Date(data.startDate) < new Date(data.endDate)
  }
  return true
}, {
  message: "La date de fin doit être postérieure à la date de début",
  path: ["endDate"]
})

// Schema de validation pour la mise à jour d'un audit
export const updateAuditSchema = z.object({
  title: z.string().min(1, "Le titre est requis").max(200, "Le titre ne peut pas dépasser 200 caractères").optional(),
  description: z.string().optional(),
  status: z.enum([
    AuditStatus.PLANNED,
    AuditStatus.IN_PROGRESS,
    AuditStatus.COMPLETED,
    AuditStatus.CANCELLED,
    AuditStatus.ON_HOLD
  ]).optional(),
  startDate: z.string().datetime("Date de début invalide").optional(),
  endDate: z.string().datetime("Date de fin invalide").optional(),
  organizationId: z.string().min(1, "L'organisation est requise").optional(),
  auditorIds: z.array(z.string()).optional(),
  leadAuditorId: z.string().optional()
}).refine((data) => {
  if (data.startDate && data.endDate) {
    return new Date(data.startDate) < new Date(data.endDate)
  }
  return true
}, {
  message: "La date de fin doit être postérieure à la date de début",
  path: ["endDate"]
})

// Schema pour les filtres de recherche
export const auditFiltersSchema = z.object({
  status: z.enum([
    AuditStatus.PLANNED,
    AuditStatus.IN_PROGRESS,
    AuditStatus.COMPLETED,
    AuditStatus.CANCELLED,
    AuditStatus.ON_HOLD
  ]).optional(),
  organizationId: z.string().optional(),
  auditorId: z.string().optional(),
  startDateFrom: z.string().datetime().optional(),
  startDateTo: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(["title", "startDate", "status", "createdAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
})

// Schema pour l'assignation d'auditeurs
export const assignAuditorsSchema = z.object({
  auditorAssignments: z.array(z.object({
    userId: z.string().min(1, "L'ID utilisateur est requis"),
    role: z.enum([
      AuditUserRole.LEAD_AUDITOR,
      AuditUserRole.AUDITOR,
      AuditUserRole.OBSERVER
    ])
  })).min(1, "Au moins un auditeur doit être assigné")
}).refine((data) => {
  // Vérifier qu'il y a exactement un auditeur principal
  const leadAuditors = data.auditorAssignments.filter(a => a.role === AuditUserRole.LEAD_AUDITOR)
  return leadAuditors.length === 1
}, {
  message: "Il doit y avoir exactement un auditeur principal",
  path: ["auditorAssignments"]
})

// Types TypeScript inférés
export type CreateAuditInput = z.infer<typeof createAuditSchema>
export type UpdateAuditInput = z.infer<typeof updateAuditSchema>
export type AuditFilters = z.infer<typeof auditFiltersSchema>
export type AssignAuditorsInput = z.infer<typeof assignAuditorsSchema>

// Type pour l'audit avec relations
export interface AuditWithRelations {
  id: string
  title: string
  description: string | null
  status: AuditStatusType
  startDate: Date
  endDate: Date | null
  createdAt: Date
  updatedAt: Date
  organizationId: string
  creatorId: string
  organization: {
    id: string
    name: string
  }
  creator: {
    id: string
    name: string | null
    email: string
  }
  auditors: Array<{
    id: string
    role: AuditUserRoleType
    user: {
      id: string
      name: string | null
      email: string
    }
  }>
  _count: {
    observations: number
    actions: number
    reports: number
  }
}

// Utilitaires pour les statuts
export const getAuditStatusLabel = (status: AuditStatusType): string => {
  const labels: Record<AuditStatusType, string> = {
    [AuditStatus.PLANNED]: "Planifié",
    [AuditStatus.IN_PROGRESS]: "En cours",
    [AuditStatus.COMPLETED]: "Terminé",
    [AuditStatus.CANCELLED]: "Annulé",
    [AuditStatus.ON_HOLD]: "En attente"
  }
  return labels[status]
}

export const getAuditStatusColor = (status: AuditStatusType): string => {
  const colors: Record<AuditStatusType, string> = {
    [AuditStatus.PLANNED]: "bg-blue-100 text-blue-800",
    [AuditStatus.IN_PROGRESS]: "bg-yellow-100 text-yellow-800",
    [AuditStatus.COMPLETED]: "bg-green-100 text-green-800",
    [AuditStatus.CANCELLED]: "bg-red-100 text-red-800",
    [AuditStatus.ON_HOLD]: "bg-gray-100 text-gray-800"
  }
  return colors[status]
}

export const getAuditUserRoleLabel = (role: AuditUserRoleType): string => {
  const labels: Record<AuditUserRoleType, string> = {
    [AuditUserRole.LEAD_AUDITOR]: "Auditeur principal",
    [AuditUserRole.AUDITOR]: "Auditeur",
    [AuditUserRole.OBSERVER]: "Observateur"
  }
  return labels[role]
}
