import { prisma } from "@/lib/prisma"
import { 
  CreateAuditInput, 
  UpdateAuditInput, 
  AuditFilters, 
  AssignAuditorsInput,
  AuditWithRelations,
  AuditUserRole
} from "@/lib/validations/audit"
import { Prisma } from "@prisma/client"

export class AuditService {
  /**
   * Créer un nouvel audit
   */
  static async createAudit(data: CreateAuditInput, creatorId: string): Promise<AuditWithRelations> {
    const { auditorIds, leadAuditorId, ...auditData } = data

    // Vérifier que l'organisation existe
    const organization = await prisma.organization.findUnique({
      where: { id: data.organizationId }
    })

    if (!organization) {
      throw new Error("Organisation non trouvée")
    }

    // Vérifier que tous les auditeurs existent
    const auditors = await prisma.user.findMany({
      where: { 
        id: { in: [...auditorIds, leadAuditorId] },
        isActive: true
      }
    })

    if (auditors.length !== [...new Set([...auditorIds, leadAuditorId])].length) {
      throw new Error("Un ou plusieurs auditeurs sont introuvables ou inactifs")
    }

    // Créer l'audit avec les assignations
    const audit = await prisma.audit.create({
      data: {
        ...auditData,
        startDate: new Date(auditData.startDate),
        endDate: auditData.endDate ? new Date(auditData.endDate) : null,
        creatorId,
        auditors: {
          create: [
            // Auditeur principal
            {
              userId: leadAuditorId,
              role: AuditUserRole.LEAD_AUDITOR
            },
            // Autres auditeurs
            ...auditorIds
              .filter(id => id !== leadAuditorId)
              .map(userId => ({
                userId,
                role: AuditUserRole.AUDITOR
              }))
          ]
        }
      },
      include: {
        organization: {
          select: { id: true, name: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        auditors: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: {
            observations: true,
            actions: true,
            reports: true
          }
        }
      }
    })

    return audit as AuditWithRelations
  }

  /**
   * Obtenir un audit par ID
   */
  static async getAuditById(id: string): Promise<AuditWithRelations | null> {
    const audit = await prisma.audit.findUnique({
      where: { id },
      include: {
        organization: {
          select: { id: true, name: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        auditors: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: {
            observations: true,
            actions: true,
            reports: true
          }
        }
      }
    })

    return audit as AuditWithRelations | null
  }

  /**
   * Obtenir la liste des audits avec filtres et pagination
   */
  static async getAudits(filters: AuditFilters): Promise<{
    audits: AuditWithRelations[]
    total: number
    page: number
    limit: number
    totalPages: number
  }> {
    const { page, limit, sortBy, sortOrder, search, ...filterParams } = filters

    // Construire les conditions de filtrage
    const where: Prisma.AuditWhereInput = {}

    if (filterParams.status) {
      where.status = filterParams.status
    }

    if (filterParams.organizationId) {
      where.organizationId = filterParams.organizationId
    }

    if (filterParams.auditorId) {
      where.auditors = {
        some: {
          userId: filterParams.auditorId
        }
      }
    }

    if (filterParams.startDateFrom || filterParams.startDateTo) {
      where.startDate = {}
      if (filterParams.startDateFrom) {
        where.startDate.gte = new Date(filterParams.startDateFrom)
      }
      if (filterParams.startDateTo) {
        where.startDate.lte = new Date(filterParams.startDateTo)
      }
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Calculer le total
    const total = await prisma.audit.count({ where })

    // Obtenir les audits avec pagination
    const audits = await prisma.audit.findMany({
      where,
      include: {
        organization: {
          select: { id: true, name: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        auditors: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: {
            observations: true,
            actions: true,
            reports: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip: (page - 1) * limit,
      take: limit
    })

    return {
      audits: audits as AuditWithRelations[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Mettre à jour un audit
   */
  static async updateAudit(id: string, data: UpdateAuditInput): Promise<AuditWithRelations> {
    const updateData: Prisma.AuditUpdateInput = {}

    if (data.title !== undefined) updateData.title = data.title
    if (data.description !== undefined) updateData.description = data.description
    if (data.status !== undefined) updateData.status = data.status
    if (data.startDate !== undefined) updateData.startDate = new Date(data.startDate)
    if (data.endDate !== undefined) updateData.endDate = data.endDate ? new Date(data.endDate) : null
    if (data.organizationId !== undefined) updateData.organizationId = data.organizationId

    const audit = await prisma.audit.update({
      where: { id },
      data: updateData,
      include: {
        organization: {
          select: { id: true, name: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        auditors: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: {
            observations: true,
            actions: true,
            reports: true
          }
        }
      }
    })

    return audit as AuditWithRelations
  }

  /**
   * Supprimer un audit
   */
  static async deleteAudit(id: string): Promise<void> {
    await prisma.audit.delete({
      where: { id }
    })
  }

  /**
   * Assigner des auditeurs à un audit
   */
  static async assignAuditors(auditId: string, data: AssignAuditorsInput): Promise<AuditWithRelations> {
    // Supprimer les assignations existantes
    await prisma.auditUser.deleteMany({
      where: { auditId }
    })

    // Créer les nouvelles assignations
    await prisma.auditUser.createMany({
      data: data.auditorAssignments.map(assignment => ({
        auditId,
        userId: assignment.userId,
        role: assignment.role
      }))
    })

    // Retourner l'audit mis à jour
    return this.getAuditById(auditId) as Promise<AuditWithRelations>
  }

  /**
   * Obtenir les statistiques des audits
   */
  static async getAuditStats(organizationId?: string): Promise<{
    total: number
    byStatus: Record<string, number>
    thisMonth: number
    thisYear: number
  }> {
    const where: Prisma.AuditWhereInput = organizationId ? { organizationId } : {}

    const [total, byStatus, thisMonth, thisYear] = await Promise.all([
      // Total des audits
      prisma.audit.count({ where }),
      
      // Par statut
      prisma.audit.groupBy({
        by: ['status'],
        where,
        _count: true
      }),
      
      // Ce mois
      prisma.audit.count({
        where: {
          ...where,
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      }),
      
      // Cette année
      prisma.audit.count({
        where: {
          ...where,
          createdAt: {
            gte: new Date(new Date().getFullYear(), 0, 1)
          }
        }
      })
    ])

    const statusCounts = byStatus.reduce((acc, item) => {
      acc[item.status] = item._count
      return acc
    }, {} as Record<string, number>)

    return {
      total,
      byStatus: statusCounts,
      thisMonth,
      thisYear
    }
  }
}
