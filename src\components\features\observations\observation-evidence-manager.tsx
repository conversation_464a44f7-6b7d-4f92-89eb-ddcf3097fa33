"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { FileUpload } from "@/components/ui/file-upload"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ObservationWithRelations } from "@/lib/validations/observation"
import { FileText, Save, Upload, AlertCircle } from "lucide-react"

interface ObservationEvidenceManagerProps {
  observation: ObservationWithRelations
  onUpdate: (evidence: string, files?: File[]) => Promise<void>
  loading?: boolean
  error?: string | null
}

export function ObservationEvidenceManager({
  observation,
  onUpdate,
  loading = false,
  error
}: ObservationEvidenceManagerProps) {
  const [evidence, setEvidence] = useState(observation.evidence || "")
  const [files, setFiles] = useState<File[]>([])
  const [hasC<PERSON><PERSON>, setHasChanges] = useState(false)

  const handleEvidenceChange = (value: string) => {
    setEvidence(value)
    setHasChanges(value !== (observation.evidence || ""))
  }

  const handleFilesSelect = (newFiles: File[]) => {
    setFiles(prev => [...prev, ...newFiles])
    setHasChanges(true)
  }

  const handleFileRemove = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
    setHasChanges(true)
  }

  const handleSave = async () => {
    try {
      await onUpdate(evidence, files.length > 0 ? files : undefined)
      setFiles([])
      setHasChanges(false)
    } catch (err) {
      console.error("Erreur lors de la sauvegarde:", err)
    }
  }

  const handleReset = () => {
    setEvidence(observation.evidence || "")
    setFiles([])
    setHasChanges(false)
  }

  return (
    <div className="space-y-6">
      {/* Description textuelle des preuves */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Description des preuves
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="evidence">
              Décrivez les preuves et éléments observés
            </Label>
            <Textarea
              id="evidence"
              placeholder="Décrivez en détail les preuves de cette observation : documents consultés, témoignages recueillis, constats visuels, etc."
              value={evidence}
              onChange={(e) => handleEvidenceChange(e.target.value)}
              disabled={loading}
              rows={6}
              className="resize-none"
            />
            <p className="text-xs text-gray-500">
              Soyez précis et factuel dans la description des preuves pour faciliter la compréhension et le suivi.
            </p>
          </div>

          {hasChanges && (
            <div className="flex items-center gap-2 pt-2 border-t">
              <Button
                onClick={handleSave}
                disabled={loading}
                size="sm"
              >
                <Save className="h-4 w-4 mr-2" />
                {loading ? "Sauvegarde..." : "Sauvegarder"}
              </Button>
              
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={loading}
                size="sm"
              >
                Annuler
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload de fichiers */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Documents et fichiers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600 mb-4">
                Ajoutez des documents, photos, captures d'écran ou autres fichiers qui appuient cette observation.
              </p>
              
              <FileUpload
                onFileSelect={handleFilesSelect}
                onFileRemove={handleFileRemove}
                files={files}
                accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                multiple={true}
                maxSize={10}
                maxFiles={10}
                disabled={loading}
              />
            </div>

            {/* Fichiers existants (simulation) */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Fichiers existants</h4>
              <div className="text-center py-8 text-gray-500">
                <FileText className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm">Aucun fichier attaché</p>
                <p className="text-xs">
                  La gestion des fichiers existants sera implémentée avec le stockage cloud
                </p>
              </div>
            </div>

            {files.length > 0 && (
              <div className="flex items-center gap-2 pt-4 border-t">
                <Button
                  onClick={handleSave}
                  disabled={loading}
                  size="sm"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {loading ? "Upload en cours..." : `Uploader ${files.length} fichier(s)`}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => setFiles([])}
                  disabled={loading}
                  size="sm"
                >
                  Annuler
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conseils */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-blue-900 mb-1">
                Conseils pour documenter les preuves
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Soyez factuel et objectif dans vos descriptions</li>
                <li>• Incluez les références des documents consultés</li>
                <li>• Mentionnez les dates, heures et lieux précis</li>
                <li>• Anonymisez les données personnelles si nécessaire</li>
                <li>• Privilégiez les formats standards pour les fichiers</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
