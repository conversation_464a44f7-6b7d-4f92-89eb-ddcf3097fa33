"use client"

import { Badge } from "@/components/ui/badge"
import { useObservationStats } from "@/hooks/use-observation-stats"
import { AlertTriangle, CheckCircle, Clock, XCircle } from "lucide-react"

interface AuditObservationsSummaryProps {
  auditId: string
  showDetailed?: boolean
}

export function AuditObservationsSummary({
  auditId,
  showDetailed = false
}: AuditObservationsSummaryProps) {
  const { stats, loading, error } = useObservationStats(auditId)
  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
        <div className="h-4 w-12 bg-gray-200 rounded animate-pulse" />
      </div>
    )
  }

  if (error || !stats) {
    return (
      <div className="flex items-center gap-1 text-xs text-gray-500">
        <AlertTriangle className="h-3 w-3" />
        <span>Erreur de chargement</span>
      </div>
    )
  }

  if (stats.total === 0) {
    return (
      <div className="flex items-center gap-1 text-xs text-gray-500">
        <AlertTriangle className="h-3 w-3" />
        <span>Aucune observation</span>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-2 text-xs">
      {/* Total */}
      <div className="flex items-center gap-1">
        <AlertTriangle className="h-3 w-3 text-gray-500" />
        <span className="font-medium">{stats.total}</span>
      </div>

      {/* Critiques */}
      {stats.criticalCount > 0 && (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 px-1 py-0 text-xs">
          {stats.criticalCount} critique{stats.criticalCount > 1 ? 's' : ''}
        </Badge>
      )}

      {/* Ouvertes */}
      {stats.openCount > 0 && (
        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 px-1 py-0 text-xs">
          {stats.openCount} ouverte{stats.openCount > 1 ? 's' : ''}
        </Badge>
      )}

      {/* Résolues */}
      {stats.resolvedCount > 0 && (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 px-1 py-0 text-xs">
          {stats.resolvedCount} résolue{stats.resolvedCount > 1 ? 's' : ''}
        </Badge>
      )}

      {/* Détails supplémentaires si demandés */}
      {showDetailed && (
        <>
          {stats.byStatus.IN_PROGRESS > 0 && (
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 px-1 py-0 text-xs">
              {stats.byStatus.IN_PROGRESS} en cours
            </Badge>
          )}
          {stats.byStatus.CLOSED > 0 && (
            <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 px-1 py-0 text-xs">
              {stats.byStatus.CLOSED} fermée{stats.byStatus.CLOSED > 1 ? 's' : ''}
            </Badge>
          )}
        </>
      )}
    </div>
  )
}
