"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { ObservationForm } from "@/components/features/observations"
import { useObservationActions } from "@/hooks/use-observation-actions"
import { useFormData } from "@/hooks/use-form-data"
import { UpdateObservationInput, ObservationWithRelations } from "@/lib/validations/observation"
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"

interface EditObservationPageProps {
  params: {
    id: string
  }
}

export default function EditObservationPage({ params }: EditObservationPageProps) {
  const router = useRouter()
  const [observation, setObservation] = useState<ObservationWithRelations | null>(null)

  const {
    loading: actionLoading,
    error: actionError,
    getObservation,
    updateObservation,
    clearError
  } = useObservationActions()

  const {
    audits,
    loading: dataLoading,
    error: dataError
  } = useFormData()

  useEffect(() => {
    const fetchObservation = async () => {
      const observationData = await getObservation(params.id)
      if (observationData) {
        setObservation(observationData)
      }
    }

    fetchObservation()
  }, [params.id, getObservation])

  const handleSubmit = async (data: UpdateObservationInput) => {
    const updatedObservation = await updateObservation(params.id, data)
    if (updatedObservation) {
      router.push(`/observations/${updatedObservation.id}`)
    }
  }

  const loading = dataLoading || actionLoading || !observation

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" disabled>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Modifier l'observation</h1>
            <p className="text-gray-600">Chargement...</p>
          </div>
        </div>

        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (dataError || actionError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Modifier l'observation</h1>
            <p className="text-gray-600">Erreur lors du chargement</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dataError || actionError}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!observation) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Observation non trouvée
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>

        <div>
          <h1 className="text-3xl font-bold magneto-title">Modifier l'observation</h1>
          <p className="text-gray-600">
            {observation.title}
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <ObservationForm
        observation={observation}
        audits={audits}
        onSubmit={handleSubmit}
        loading={actionLoading}
        error={actionError}
      />
    </div>
  )
}
