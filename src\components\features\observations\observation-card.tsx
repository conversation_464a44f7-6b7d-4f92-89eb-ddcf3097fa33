import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ObservationSeverityBadge } from "./observation-severity-badge"
import { ObservationStatusBadge } from "./observation-status-badge"
import { ObservationWithRelations } from "@/lib/validations/observation"
import { Eye, Edit, Trash2, CheckCircle, XCircle, RotateCcw, FileText, Calendar } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ObservationCardProps {
  observation: ObservationWithRelations
  onView?: (observation: ObservationWithRelations) => void
  onEdit?: (observation: ObservationWithRelations) => void
  onDelete?: (observation: ObservationWithRelations) => void
  onResolve?: (observation: ObservationWithRelations) => void
  onClose?: (observation: ObservationWithRelations) => void
  onReopen?: (observation: ObservationWithRelations) => void
  showActions?: boolean
  showAuditInfo?: boolean
}

export function ObservationCard({ 
  observation, 
  onView, 
  onEdit, 
  onDelete,
  onResolve,
  onClose,
  onReopen,
  showActions = true,
  showAuditInfo = true
}: ObservationCardProps) {
  const formatDate = (date: Date) => {
    return format(date, "dd MMM yyyy", { locale: fr })
  }

  const canResolve = observation.status === "OPEN" || observation.status === "IN_PROGRESS"
  const canClose = observation.status === "RESOLVED"
  const canReopen = observation.status === "RESOLVED" || observation.status === "CLOSED"
  const canEdit = observation.status !== "CLOSED" && observation.status !== "REJECTED"
  const canDelete = observation.status === "OPEN"

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-2">
              {observation.title}
            </CardTitle>
            {showAuditInfo && (
              <CardDescription className="text-sm text-gray-600">
                Audit: {observation.audit.title}
              </CardDescription>
            )}
          </div>
          <div className="flex flex-col gap-2 ml-4">
            <ObservationSeverityBadge severity={observation.severity} />
            <ObservationStatusBadge status={observation.status} />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Description */}
        <p className="text-sm text-gray-700 line-clamp-3">
          {observation.description}
        </p>

        {/* Informations */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(observation.createdAt)}</span>
            </div>
            
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              <span>{observation._count.actions} action{observation._count.actions > 1 ? 's' : ''}</span>
            </div>
          </div>
          
          {showAuditInfo && (
            <Badge variant="outline" className="text-xs">
              {observation.audit.organization.name}
            </Badge>
          )}
        </div>

        {/* Preuves */}
        {observation.evidence && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-xs font-medium text-gray-700 mb-1">Preuves</p>
            <p className="text-sm text-gray-600 line-clamp-2">{observation.evidence}</p>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center gap-2 pt-2 border-t">
            {onView && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onView(observation)}
                className="flex items-center gap-1"
              >
                <Eye className="h-3 w-3" />
                Voir
              </Button>
            )}
            
            {onEdit && canEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(observation)}
                className="flex items-center gap-1"
              >
                <Edit className="h-3 w-3" />
                Modifier
              </Button>
            )}

            {onResolve && canResolve && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onResolve(observation)}
                className="flex items-center gap-1 text-green-600 hover:text-green-700 hover:bg-green-50"
              >
                <CheckCircle className="h-3 w-3" />
                Résoudre
              </Button>
            )}

            {onClose && canClose && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onClose(observation)}
                className="flex items-center gap-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
              >
                <XCircle className="h-3 w-3" />
                Fermer
              </Button>
            )}

            {onReopen && canReopen && observation.status !== "OPEN" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onReopen(observation)}
                className="flex items-center gap-1 text-orange-600 hover:text-orange-700 hover:bg-orange-50"
              >
                <RotateCcw className="h-3 w-3" />
                Rouvrir
              </Button>
            )}
            
            {onDelete && canDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(observation)}
                className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3" />
                Supprimer
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
