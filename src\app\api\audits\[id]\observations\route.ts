import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ObservationService } from "@/lib/services/observation-service"
import { AuditService } from "@/lib/services/audit-service"
import { hasPermission, UserRole } from "@/lib/validations/user"

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * GET /api/audits/[id]/observations - Obtenir les observations d'un audit
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'audit existe et que l'utilisateur y a accès
      const audit = await AuditService.getAuditById(params.id)
      
      if (!audit) {
        return NextResponse.json(
          { success: false, error: "Audit non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier l'accès selon le rôle
      const canAccess = 
        user.role === UserRole.SUPER_ADMIN ||
        audit.organizationId === user.organizationId ||
        audit.auditors.some(a => a.user.id === user.id) ||
        audit.creatorId === user.id

      if (!canAccess) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé à cet audit" },
          { status: 403 }
        )
      }

      const observations = await ObservationService.getObservationsByAudit(params.id)

      return NextResponse.json({
        success: true,
        data: observations
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des observations:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
