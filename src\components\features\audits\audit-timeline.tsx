import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Calendar, Users, FileText, Clock, CheckCircle } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface AuditTimelineProps {
  audit: AuditWithRelations
}

interface TimelineEvent {
  id: string
  type: "creation" | "status_change" | "auditor_assigned" | "observation" | "action" | "report"
  title: string
  description: string
  date: Date
  icon: React.ComponentType<{ className?: string }>
  color: string
}

export function AuditTimeline({ audit }: AuditTimelineProps) {
  const formatDateTime = (date: Date) => {
    return format(date, "dd/MM/yyyy à HH:mm", { locale: fr })
  }

  // Générer les événements de la timeline
  const events: TimelineEvent[] = [
    {
      id: "creation",
      type: "creation",
      title: "<PERSON>t cré<PERSON>",
      description: `<PERSON><PERSON><PERSON> par ${audit.creator.name || audit.creator.email}`,
      date: audit.createdAt,
      icon: Calendar,
      color: "bg-blue-500"
    }
  ]

  // Ajouter les événements d'assignation d'auditeurs
  audit.auditors.forEach((auditor, index) => {
    events.push({
      id: `auditor-${auditor.id}`,
      type: "auditor_assigned",
      title: "Auditeur assigné",
      description: `${auditor.user.name || auditor.user.email} - ${
        auditor.role === "LEAD_AUDITOR" ? "Auditeur principal" : 
        auditor.role === "AUDITOR" ? "Auditeur" : "Observateur"
      }`,
      date: audit.createdAt, // En réalité, il faudrait avoir la date d'assignation
      icon: Users,
      color: auditor.role === "LEAD_AUDITOR" ? "bg-purple-500" : "bg-green-500"
    })
  })

  // Ajouter des événements fictifs pour la démonstration
  if (audit.status !== "PLANNED") {
    events.push({
      id: "status-in-progress",
      type: "status_change",
      title: "Audit démarré",
      description: "L'audit est passé en cours d'exécution",
      date: audit.startDate,
      icon: CheckCircle,
      color: "bg-yellow-500"
    })
  }

  if (audit.status === "COMPLETED") {
    events.push({
      id: "status-completed",
      type: "status_change",
      title: "Audit terminé",
      description: "L'audit a été marqué comme terminé",
      date: audit.endDate || audit.updatedAt,
      icon: CheckCircle,
      color: "bg-green-600"
    })
  }

  // Trier les événements par date
  events.sort((a, b) => a.date.getTime() - b.date.getTime())

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Historique de l'audit
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          {/* Ligne verticale */}
          <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200" />
          
          <div className="space-y-6">
            {events.map((event, index) => {
              const Icon = event.icon
              return (
                <div key={event.id} className="relative flex items-start gap-4">
                  {/* Icône de l'événement */}
                  <div className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full ${event.color}`}>
                    <Icon className="h-4 w-4 text-white" />
                  </div>
                  
                  {/* Contenu de l'événement */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">
                        {event.title}
                      </h4>
                      <time className="text-xs text-gray-500">
                        {formatDateTime(event.date)}
                      </time>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {event.description}
                    </p>
                    
                    {/* Badge pour le type d'événement */}
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs">
                        {event.type === "creation" && "Création"}
                        {event.type === "status_change" && "Changement de statut"}
                        {event.type === "auditor_assigned" && "Assignation"}
                        {event.type === "observation" && "Observation"}
                        {event.type === "action" && "Action"}
                        {event.type === "report" && "Rapport"}
                      </Badge>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
          
          {/* Événements futurs ou à venir */}
          {audit.status === "PLANNED" && (
            <div className="relative flex items-start gap-4 mt-6 opacity-50">
              <div className="relative z-10 flex items-center justify-center w-8 h-8 rounded-full bg-gray-300">
                <CheckCircle className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-500">
                  Démarrage de l'audit
                </h4>
                <p className="text-sm text-gray-400 mt-1">
                  Prévu le {format(audit.startDate, "dd/MM/yyyy", { locale: fr })}
                </p>
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs text-gray-400">
                    À venir
                  </Badge>
                </div>
              </div>
            </div>
          )}
          
          {audit.endDate && audit.status !== "COMPLETED" && (
            <div className="relative flex items-start gap-4 mt-6 opacity-50">
              <div className="relative z-10 flex items-center justify-center w-8 h-8 rounded-full bg-gray-300">
                <FileText className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-500">
                  Fin prévue de l'audit
                </h4>
                <p className="text-sm text-gray-400 mt-1">
                  Prévu le {format(audit.endDate, "dd/MM/yyyy", { locale: fr })}
                </p>
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs text-gray-400">
                    À venir
                  </Badge>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {events.length === 1 && (
          <div className="text-center py-8 text-gray-500">
            <Clock className="mx-auto h-8 w-8 text-gray-400 mb-2" />
            <p className="text-sm">L'historique des activités apparaîtra ici au fur et à mesure de l'avancement de l'audit.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
