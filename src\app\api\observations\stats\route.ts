import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ObservationService } from "@/lib/services/observation-service"
import { hasPermission, UserRole } from "@/lib/validations/user"

/**
 * GET /api/observations/stats - Obtenir les statistiques des observations
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Permettre de filtrer par audit via query param
      const { searchParams } = new URL(req.url)
      const auditId = searchParams.get('auditId')

      const stats = await ObservationService.getObservationStats(auditId || undefined)

      return NextResponse.json({
        success: true,
        data: stats
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des statistiques:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
