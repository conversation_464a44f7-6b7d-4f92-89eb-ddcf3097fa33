"use client"

import { useSimpleAuth, useSimplePermissions } from "@/lib/auth/simple-auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle } from "lucide-react"

export default function TestSidebarVisibilityPage() {
  const { user, isAuthenticated, signIn, signOut } = useSimpleAuth()
  const permissions = useSimplePermissions()

  const handleQuickLogin = async () => {
    try {
      await signIn('<EMAIL>', 'password123')
    } catch (error) {
      console.error('Erreur de connexion:', error)
    }
  }

  const sidebarItems = [
    {
      name: "Dashboard",
      permission: null,
      visible: true
    },
    {
      name: "Audits",
      permission: "audits:read",
      visible: permissions.checkPermission('audits:read')
    },
    {
      name: "Observations", 
      permission: "observations:read",
      visible: permissions.checkPermission('observations:read')
    },
    {
      name: "Rapports",
      permission: "reports:read", 
      visible: permissions.checkPermission('reports:read')
    },
    {
      name: "Utilisateurs",
      permission: "users:read",
      visible: permissions.checkPermission('users:read')
    }
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Test Visibilité Sidebar</h1>
        <div className="space-x-2">
          {!isAuthenticated ? (
            <Button onClick={handleQuickLogin}>
              Connexion Rapide (Admin)
            </Button>
          ) : (
            <Button variant="outline" onClick={signOut}>
              Déconnexion
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* État Authentification */}
        <Card>
          <CardHeader>
            <CardTitle>État Authentification</CardTitle>
            <CardDescription>État actuel de l'utilisateur</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>Connecté:</strong> 
              <Badge variant={isAuthenticated ? "default" : "secondary"} className="ml-2">
                {isAuthenticated ? "Oui" : "Non"}
              </Badge>
            </div>
            {user && (
              <>
                <div>
                  <strong>Email:</strong> {user.email}
                </div>
                <div>
                  <strong>Rôle:</strong> 
                  <Badge variant="outline" className="ml-2">
                    {user.role}
                  </Badge>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Visibilité Sidebar */}
        <Card>
          <CardHeader>
            <CardTitle>Visibilité des Éléments Sidebar</CardTitle>
            <CardDescription>Quels éléments sont visibles dans le sidebar</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {sidebarItems.map((item) => (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {item.visible ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className={item.visible ? "text-green-700" : "text-red-700"}>
                    {item.name}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  {item.permission || "Toujours visible"}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Résumé des Permissions */}
      <Card>
        <CardHeader>
          <CardTitle>Résumé des Permissions</CardTitle>
          <CardDescription>Toutes les permissions de l'utilisateur actuel</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Audits</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Lire:</span>
                  <Badge variant={permissions.canManageAudits ? "default" : "secondary"} className="text-xs">
                    {permissions.canManageAudits ? "✓" : "✗"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Créer:</span>
                  <Badge variant={permissions.canCreateAudits ? "default" : "secondary"} className="text-xs">
                    {permissions.canCreateAudits ? "✓" : "✗"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Modifier:</span>
                  <Badge variant={permissions.canUpdateAudits ? "default" : "secondary"} className="text-xs">
                    {permissions.canUpdateAudits ? "✓" : "✗"}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold">Observations</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Lire:</span>
                  <Badge variant={permissions.canManageObservations ? "default" : "secondary"} className="text-xs">
                    {permissions.canManageObservations ? "✓" : "✗"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Créer:</span>
                  <Badge variant={permissions.canCreateObservations ? "default" : "secondary"} className="text-xs">
                    {permissions.canCreateObservations ? "✓" : "✗"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Modifier:</span>
                  <Badge variant={permissions.canUpdateObservations ? "default" : "secondary"} className="text-xs">
                    {permissions.canUpdateObservations ? "✓" : "✗"}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold">Rapports</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Lire:</span>
                  <Badge variant={permissions.canManageReports ? "default" : "secondary"} className="text-xs">
                    {permissions.canManageReports ? "✓" : "✗"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Créer:</span>
                  <Badge variant={permissions.canCreateReports ? "default" : "secondary"} className="text-xs">
                    {permissions.canCreateReports ? "✓" : "✗"}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold">Utilisateurs</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Lire:</span>
                  <Badge variant={permissions.canManageUsers ? "default" : "secondary"} className="text-xs">
                    {permissions.canManageUsers ? "✓" : "✗"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Créer:</span>
                  <Badge variant={permissions.canCreateUsers ? "default" : "secondary"} className="text-xs">
                    {permissions.canCreateUsers ? "✓" : "✗"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {!isAuthenticated && (
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
            <CardDescription>Comment tester la visibilité du sidebar</CardDescription>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Cliquez sur "Connexion Rapide (Admin)" pour vous connecter</li>
              <li>Vérifiez que les éléments Audits et Observations apparaissent comme "visibles" ci-dessus</li>
              <li>Retournez au dashboard principal pour voir le sidebar</li>
              <li>Les modules Audits et Observations devraient maintenant être visibles dans le sidebar</li>
            </ol>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
