@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Magneto Color Scheme */
  /* Workspace background: #FDFDFD */
  --background: #FDFDFD;
  /* Text color: black */
  --foreground: #000000;
  /* Card background: #F2F2F2 */
  --card: #F2F2F2;
  --card-foreground: #000000;
  --popover: #FDFDFD;
  --popover-foreground: #000000;
  /* Primary button: #2E427D background with white text */
  --primary: #2E427D;
  --primary-foreground: #FFFFFF;
  /* Secondary colors */
  --secondary: #F2F2F2;
  --secondary-foreground: #000000;
  --muted: #F2F2F2;
  /* Placeholder text: light gray */
  --muted-foreground: #8D97AE;
  --accent: #E44C43;
  --accent-foreground: #FFFFFF;
  --destructive: #E0483F;
  /* Border: light gray */
  --border: #E5E7EB;
  --input: #E5E7EB;
  --ring: #2E427D;
  --chart-1: #2E427D;
  --chart-2: #E44C43;
  --chart-3: #434D68;
  --chart-4: #6A7289;
  --chart-5: #8D97AE;
  /* Sidebar: #434D68 background with #8D97AE text */
  --sidebar: #434D68;
  --sidebar-foreground: #8D97AE;
  --sidebar-primary: #2E427D;
  --sidebar-primary-foreground: #FFFFFF;
  /* Selected menu: #6A7289 */
  --sidebar-accent: #6A7289;
  --sidebar-accent-foreground: #FFFFFF;
  --sidebar-border: #6A7289;
  --sidebar-ring: #2E427D;
  /* Navbar: #E44C43 background with white text */
  --navbar: #E44C43;
  --navbar-foreground: #FFFFFF;
}

.dark {
  /* Dark mode maintains the same Magneto color scheme */
  --background: #1a1a1a;
  --foreground: #FDFDFD;
  --card: #2a2a2a;
  --card-foreground: #FDFDFD;
  --popover: #2a2a2a;
  --popover-foreground: #FDFDFD;
  --primary: #2E427D;
  --primary-foreground: #FFFFFF;
  --secondary: #3a3a3a;
  --secondary-foreground: #FDFDFD;
  --muted: #3a3a3a;
  --muted-foreground: #8D97AE;
  --accent: #E44C43;
  --accent-foreground: #FFFFFF;
  --destructive: #E0483F;
  --border: #4a4a4a;
  --input: #4a4a4a;
  --ring: #2E427D;
  --chart-1: #2E427D;
  --chart-2: #E44C43;
  --chart-3: #434D68;
  --chart-4: #6A7289;
  --chart-5: #8D97AE;
  --sidebar: #434D68;
  --sidebar-foreground: #8D97AE;
  --sidebar-primary: #2E427D;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #6A7289;
  --sidebar-accent-foreground: #FFFFFF;
  --sidebar-border: #6A7289;
  --sidebar-ring: #2E427D;
  --navbar: #E44C43;
  --navbar-foreground: #FFFFFF;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', 'Poppins', 'Roboto', sans-serif;
  }
}

@layer components {
  /* Magneto specific components */
  .magneto-sidebar {
    @apply bg-[#434D68] text-[#8D97AE];
  }

  .magneto-sidebar-selected {
    @apply bg-[#6A7289] text-white;
  }

  .magneto-navbar {
    @apply bg-[#E44C43] text-white;
  }

  .magneto-workspace {
    @apply bg-[#FDFDFD];
  }

  .magneto-button {
    @apply bg-[#2E427D] text-white hover:bg-[#2E427D]/90;
  }

  .magneto-input {
    @apply border-gray-300 text-black placeholder:text-gray-400;
  }

  .magneto-title {
    @apply text-black font-semibold;
  }

  .magneto-label {
    @apply text-black;
  }
}
