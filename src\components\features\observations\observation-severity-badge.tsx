import { Badge } from "@/components/ui/badge"
import { 
  ObservationSeverityType, 
  getObservationSeverityLabel, 
  getObservationSeverityColor 
} from "@/lib/validations/observation"

interface ObservationSeverityBadgeProps {
  severity: ObservationSeverityType
  className?: string
}

export function ObservationSeverityBadge({ severity, className }: ObservationSeverityBadgeProps) {
  return (
    <Badge 
      variant="outline" 
      className={`${getObservationSeverityColor(severity)} ${className}`}
    >
      {getObservationSeverityLabel(severity)}
    </Badge>
  )
}
