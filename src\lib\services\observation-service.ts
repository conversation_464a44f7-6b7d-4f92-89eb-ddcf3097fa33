import { prisma } from "@/lib/prisma"
import { 
  CreateObservationInput, 
  UpdateObservationInput, 
  ObservationFilters, 
  ObservationWithRelations,
  ObservationStats,
  calculateObservationStats
} from "@/lib/validations/observation"
import { Prisma } from "@prisma/client"

export class ObservationService {
  /**
   * Créer une nouvelle observation
   */
  static async createObservation(data: CreateObservationInput): Promise<ObservationWithRelations> {
    // Vérifier que l'audit existe
    const audit = await prisma.audit.findUnique({
      where: { id: data.auditId },
      include: {
        organization: {
          select: { id: true, name: true }
        }
      }
    })

    if (!audit) {
      throw new Error("Audit non trouvé")
    }

    const observation = await prisma.observation.create({
      data: {
        title: data.title,
        description: data.description,
        severity: data.severity,
        evidence: data.evidence,
        auditId: data.auditId
      },
      include: {
        audit: {
          select: {
            id: true,
            title: true,
            status: true,
            organization: {
              select: { id: true, name: true }
            }
          }
        },
        actions: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: { actions: true }
        }
      }
    })

    return observation as ObservationWithRelations
  }

  /**
   * Obtenir une observation par ID
   */
  static async getObservationById(id: string): Promise<ObservationWithRelations | null> {
    const observation = await prisma.observation.findUnique({
      where: { id },
      include: {
        audit: {
          select: {
            id: true,
            title: true,
            status: true,
            organization: {
              select: { id: true, name: true }
            }
          }
        },
        actions: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: { actions: true }
        }
      }
    })

    return observation as ObservationWithRelations | null
  }

  /**
   * Obtenir la liste des observations avec filtres et pagination
   */
  static async getObservations(filters: ObservationFilters): Promise<{
    observations: ObservationWithRelations[]
    total: number
    page: number
    limit: number
    totalPages: number
  }> {
    const { page, limit, sortBy, sortOrder, search, ...filterParams } = filters

    // Construire les conditions de filtrage
    const where: Prisma.ObservationWhereInput = {}

    if (filterParams.auditId) {
      where.auditId = filterParams.auditId
    }

    if (filterParams.severity) {
      where.severity = filterParams.severity
    }

    if (filterParams.status) {
      where.status = filterParams.status
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Calculer le total
    const total = await prisma.observation.count({ where })

    // Obtenir les observations avec pagination
    const observations = await prisma.observation.findMany({
      where,
      include: {
        audit: {
          select: {
            id: true,
            title: true,
            status: true,
            organization: {
              select: { id: true, name: true }
            }
          }
        },
        actions: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: { actions: true }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip: (page - 1) * limit,
      take: limit
    })

    return {
      observations: observations as ObservationWithRelations[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Obtenir les observations d'un audit spécifique
   */
  static async getObservationsByAudit(auditId: string): Promise<ObservationWithRelations[]> {
    const observations = await prisma.observation.findMany({
      where: { auditId },
      include: {
        audit: {
          select: {
            id: true,
            title: true,
            status: true,
            organization: {
              select: { id: true, name: true }
            }
          }
        },
        actions: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: { actions: true }
        }
      },
      orderBy: [
        { severity: 'desc' }, // Critiques en premier
        { createdAt: 'desc' }
      ]
    })

    return observations as ObservationWithRelations[]
  }

  /**
   * Mettre à jour une observation
   */
  static async updateObservation(id: string, data: UpdateObservationInput): Promise<ObservationWithRelations> {
    const updateData: Prisma.ObservationUpdateInput = {}

    if (data.title !== undefined) updateData.title = data.title
    if (data.description !== undefined) updateData.description = data.description
    if (data.severity !== undefined) updateData.severity = data.severity
    if (data.status !== undefined) updateData.status = data.status
    if (data.evidence !== undefined) updateData.evidence = data.evidence

    const observation = await prisma.observation.update({
      where: { id },
      data: updateData,
      include: {
        audit: {
          select: {
            id: true,
            title: true,
            status: true,
            organization: {
              select: { id: true, name: true }
            }
          }
        },
        actions: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: { actions: true }
        }
      }
    })

    return observation as ObservationWithRelations
  }

  /**
   * Supprimer une observation
   */
  static async deleteObservation(id: string): Promise<void> {
    await prisma.observation.delete({
      where: { id }
    })
  }

  /**
   * Obtenir les statistiques des observations
   */
  static async getObservationStats(auditId?: string): Promise<ObservationStats> {
    const where: Prisma.ObservationWhereInput = auditId ? { auditId } : {}

    const observations = await prisma.observation.findMany({
      where,
      select: {
        severity: true,
        status: true
      }
    })

    // Convertir en format attendu pour le calcul des stats
    const observationsForStats = observations.map(obs => ({
      severity: obs.severity as any,
      status: obs.status as any
    })) as ObservationWithRelations[]

    return calculateObservationStats(observationsForStats)
  }

  /**
   * Obtenir les observations critiques ouvertes
   */
  static async getCriticalOpenObservations(auditId?: string): Promise<ObservationWithRelations[]> {
    const where: Prisma.ObservationWhereInput = {
      severity: 'CRITICAL',
      status: 'OPEN'
    }

    if (auditId) {
      where.auditId = auditId
    }

    const observations = await prisma.observation.findMany({
      where,
      include: {
        audit: {
          select: {
            id: true,
            title: true,
            status: true,
            organization: {
              select: { id: true, name: true }
            }
          }
        },
        actions: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        _count: {
          select: { actions: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return observations as ObservationWithRelations[]
  }

  /**
   * Marquer une observation comme résolue
   */
  static async resolveObservation(id: string): Promise<ObservationWithRelations> {
    return this.updateObservation(id, { status: 'RESOLVED' })
  }

  /**
   * Marquer une observation comme fermée
   */
  static async closeObservation(id: string): Promise<ObservationWithRelations> {
    return this.updateObservation(id, { status: 'CLOSED' })
  }

  /**
   * Rouvrir une observation
   */
  static async reopenObservation(id: string): Promise<ObservationWithRelations> {
    return this.updateObservation(id, { status: 'OPEN' })
  }
}
