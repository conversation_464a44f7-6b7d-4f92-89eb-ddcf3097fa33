import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Calendar, Users, FileText, AlertTriangle, Clock, CheckCircle } from "lucide-react"
import { format, differenceInDays } from "date-fns"
import { fr } from "date-fns/locale"

interface AuditStatsCardsProps {
  audit: AuditWithRelations
}

export function AuditStatsCards({ audit }: AuditStatsCardsProps) {
  const formatDate = (date: Date) => {
    return format(date, "dd MMMM yyyy", { locale: fr })
  }

  const getDuration = () => {
    if (!audit.endDate) return "Non définie"
    const days = differenceInDays(audit.endDate, audit.startDate)
    return `${days} jour${days > 1 ? 's' : ''}`
  }

  const getProgress = () => {
    const total = audit._count.observations + audit._count.actions
    if (total === 0) return 0
    
    // Pour l'exemple, on considère que les actions terminées représentent le progrès
    // Dans une vraie implémentation, il faudrait compter les actions avec statut "COMPLETED"
    return Math.round((audit._count.actions / total) * 100)
  }

  const leadAuditor = audit.auditors.find(a => a.role === "LEAD_AUDITOR")

  const stats = [
    {
      title: "Dates",
      icon: Calendar,
      content: (
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-500">Début</p>
            <p className="font-medium">{formatDate(audit.startDate)}</p>
          </div>
          {audit.endDate && (
            <div>
              <p className="text-sm text-gray-500">Fin</p>
              <p className="font-medium">{formatDate(audit.endDate)}</p>
            </div>
          )}
          <div>
            <p className="text-sm text-gray-500">Durée</p>
            <p className="font-medium">{getDuration()}</p>
          </div>
        </div>
      )
    },
    {
      title: "Équipe",
      icon: Users,
      content: (
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-500">Auditeurs</p>
            <p className="font-medium">{audit.auditors.length}</p>
          </div>
          {leadAuditor && (
            <div>
              <p className="text-sm text-gray-500">Auditeur principal</p>
              <p className="font-medium text-sm">{leadAuditor.user.name || leadAuditor.user.email}</p>
            </div>
          )}
          <div>
            <p className="text-sm text-gray-500">Créé par</p>
            <p className="font-medium text-sm">{audit.creator.name || audit.creator.email}</p>
          </div>
        </div>
      )
    },
    {
      title: "Observations",
      icon: AlertTriangle,
      content: (
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-500">Total</p>
            <p className="font-medium text-2xl">{audit._count.observations}</p>
          </div>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center">
              <p className="text-red-600 font-medium">0</p>
              <p className="text-gray-500">Critiques</p>
            </div>
            <div className="text-center">
              <p className="text-yellow-600 font-medium">0</p>
              <p className="text-gray-500">Majeures</p>
            </div>
            <div className="text-center">
              <p className="text-blue-600 font-medium">0</p>
              <p className="text-gray-500">Mineures</p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Actions",
      icon: CheckCircle,
      content: (
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-500">Total</p>
            <p className="font-medium text-2xl">{audit._count.actions}</p>
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="text-center">
              <p className="text-green-600 font-medium">0</p>
              <p className="text-gray-500">Terminées</p>
            </div>
            <div className="text-center">
              <p className="text-orange-600 font-medium">0</p>
              <p className="text-gray-500">En cours</p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Progression",
      icon: Clock,
      content: (
        <div className="space-y-2">
          <div>
            <p className="text-sm text-gray-500">Avancement</p>
            <p className="font-medium text-2xl">{getProgress()}%</p>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgress()}%` }}
            />
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="text-center">
              <p className="text-gray-600 font-medium">{audit._count.reports}</p>
              <p className="text-gray-500">Rapports</p>
            </div>
            <div className="text-center">
              <p className="text-gray-600 font-medium">
                {format(audit.createdAt, "dd/MM/yyyy")}
              </p>
              <p className="text-gray-500">Créé le</p>
            </div>
          </div>
        </div>
      )
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index}>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Icon className="h-4 w-4" />
                {stat.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stat.content}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
