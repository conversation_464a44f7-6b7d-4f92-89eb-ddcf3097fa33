"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  ObservationTable, 
  ObservationFiltersComponent, 
  ObservationStatsComponent 
} from "@/components/features/observations"
import { AuditPagination } from "@/components/features/audits"
import { useObservations } from "@/hooks/use-observations"
import { useObservationActions } from "@/hooks/use-observation-actions"
import { ObservationWithRelations } from "@/lib/validations/observation"
import { Plus, Download, Trash2, AlertCircle, CheckCircle, XCircle, RotateCcw } from "lucide-react"
import { useRouter } from "next/navigation"

export default function ObservationsPage() {
  const router = useRouter()
  const [selectedObservationIds, setSelectedObservationIds] = useState<string[]>([])
  
  const {
    observations,
    total,
    totalPages,
    loading,
    error,
    filters,
    setFilters,
    refetch
  } = useObservations()

  const {
    loading: actionLoading,
    error: actionError,
    deleteObservation,
    resolveObservation,
    closeObservation,
    reopenObservation,
    clearError
  } = useObservationActions()

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handleItemsPerPageChange = (limit: number) => {
    setFilters({ ...filters, limit, page: 1 })
  }

  const handleViewObservation = (observation: ObservationWithRelations) => {
    router.push(`/observations/${observation.id}`)
  }

  const handleEditObservation = (observation: ObservationWithRelations) => {
    router.push(`/observations/${observation.id}/edit`)
  }

  const handleDeleteObservation = async (observation: ObservationWithRelations) => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'observation "${observation.title}" ?`)) {
      const success = await deleteObservation(observation.id)
      if (success) {
        await refetch()
      }
    }
  }

  const handleResolveObservation = async (observation: ObservationWithRelations) => {
    if (confirm(`Marquer l'observation "${observation.title}" comme résolue ?`)) {
      const resolved = await resolveObservation(observation.id)
      if (resolved) {
        await refetch()
      }
    }
  }

  const handleCloseObservation = async (observation: ObservationWithRelations) => {
    if (confirm(`Fermer définitivement l'observation "${observation.title}" ?`)) {
      const closed = await closeObservation(observation.id)
      if (closed) {
        await refetch()
      }
    }
  }

  const handleReopenObservation = async (observation: ObservationWithRelations) => {
    if (confirm(`Rouvrir l'observation "${observation.title}" ?`)) {
      const reopened = await reopenObservation(observation.id)
      if (reopened) {
        await refetch()
      }
    }
  }

  const handleBulkDelete = async () => {
    if (selectedObservationIds.length === 0) return
    
    if (confirm(`Êtes-vous sûr de vouloir supprimer ${selectedObservationIds.length} observation(s) ?`)) {
      // Supprimer chaque observation sélectionnée
      for (const observationId of selectedObservationIds) {
        await deleteObservation(observationId)
      }
      setSelectedObservationIds([])
      await refetch()
    }
  }

  const handleCreateObservation = () => {
    router.push("/observations/new")
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold magneto-title">Gestion des observations</h1>
          <p className="text-gray-600">
            Gérez les observations d'audit et suivez leur résolution
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {selectedObservationIds.length > 0 && (
            <Button
              variant="outline"
              onClick={handleBulkDelete}
              disabled={actionLoading}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Supprimer ({selectedObservationIds.length})
            </Button>
          )}
          
          <Button
            variant="outline"
            onClick={() => {/* TODO: Implémenter l'export */}}
            disabled={loading}
          >
            <Download className="h-4 w-4 mr-2" />
            Exporter
          </Button>
          
          <Button onClick={handleCreateObservation}>
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle observation
          </Button>
        </div>
      </div>

      {/* Erreurs */}
      {(error || actionError) && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || actionError}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Statistiques */}
      <ObservationStatsComponent 
        stats={{
          total,
          bySeverity: { LOW: 0, MEDIUM: 0, HIGH: 0, CRITICAL: 0 },
          byStatus: { OPEN: 0, IN_PROGRESS: 0, RESOLVED: 0, CLOSED: 0, REJECTED: 0 },
          openCount: 0,
          criticalCount: 0,
          resolvedCount: 0
        }}
        loading={loading}
      />

      {/* Filtres */}
      <ObservationFiltersComponent
        filters={filters}
        onFiltersChange={setFilters}
        loading={loading}
      />

      {/* Tableau des observations */}
      <Card>
        <CardHeader>
          <CardTitle>
            Observations ({total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <ObservationTable
              observations={observations}
              onView={handleViewObservation}
              onEdit={handleEditObservation}
              onDelete={handleDeleteObservation}
              onResolve={handleResolveObservation}
              onClose={handleCloseObservation}
              onReopen={handleReopenObservation}
              onSelectionChange={setSelectedObservationIds}
              loading={loading}
              showAuditInfo={true}
            />
            
            {total > 0 && (
              <AuditPagination
                currentPage={filters.page}
                totalPages={totalPages}
                totalItems={total}
                itemsPerPage={filters.limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                loading={loading}
              />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
