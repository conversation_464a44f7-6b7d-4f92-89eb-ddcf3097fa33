"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useState } from "react"

export default function TestSimplePage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("password")

  const handleTestAPI = async () => {
    try {
      const response = await fetch("/api/test")
      const data = await response.json()
      console.log("API Test:", data)
      alert(`API Test: ${data.message}`)
    } catch (error) {
      console.error("Erreur API:", error)
      alert("Erreur lors du test API")
    }
  }

  const handleTestAuth = async () => {
    try {
      // D'abord, testons les informations sur Better Auth
      const infoResponse = await fetch("/api/auth-info")
      const infoData = await infoResponse.json()
      console.log("Auth Info:", infoData)

      // Ensuite, testons différentes routes possibles
      const routes = [
        "/api/auth/session",
        "/api/auth/get-session",
        "/api/auth/me",
        "/api/auth/user"
      ]

      const results = []

      for (const route of routes) {
        try {
          const response = await fetch(route)
          const text = await response.text()
          let data
          try {
            data = JSON.parse(text)
          } catch {
            data = text
          }
          results.push({
            route,
            status: response.status,
            data: data
          })
        } catch (error: any) {
          results.push({
            route,
            status: "error",
            data: error.message
          })
        }
      }

      console.log("Auth Routes Test:", results)
      alert(`Auth Info: ${JSON.stringify(infoData, null, 2)}\n\nRoutes Test: ${JSON.stringify(results, null, 2)}`)
    } catch (error) {
      console.error("Erreur Auth:", error)
      alert("Erreur lors du test Auth: " + (error as Error).message)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Test Simple</h1>
        <p className="text-gray-600">
          Page de test pour vérifier les fonctionnalités de base
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test API */}
        <Card>
          <CardHeader>
            <CardTitle>Test API</CardTitle>
            <CardDescription>Tester la connectivité API de base</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleTestAPI} className="magneto-button">
              Tester l'API
            </Button>
          </CardContent>
        </Card>

        {/* Test Auth */}
        <Card>
          <CardHeader>
            <CardTitle>Test Authentification</CardTitle>
            <CardDescription>Tester l'API d'authentification</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleTestAuth} className="magneto-button">
              Tester Auth API
            </Button>
          </CardContent>
        </Card>

        {/* Formulaire de test */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Formulaire de test</CardTitle>
            <CardDescription>Tester les composants de formulaire</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="magneto-input"
              />
            </div>
            <div>
              <Label htmlFor="password">Mot de passe</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="magneto-input"
              />
            </div>
            <Button className="magneto-button">
              Bouton de test
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
