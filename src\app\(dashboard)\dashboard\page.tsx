import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ClipboardList, FileText, Users, Building2 } from "lucide-react"

export default function DashboardPage() {
  const stats = [
    {
      title: "Audits actifs",
      value: "12",
      description: "Audits en cours",
      icon: ClipboardList,
      color: "text-blue-600",
    },
    {
      title: "Rapports",
      value: "45",
      description: "Rapports générés",
      icon: FileText,
      color: "text-green-600",
    },
    {
      title: "Utilisateurs",
      value: "28",
      description: "Utilisateurs actifs",
      icon: Users,
      color: "text-purple-600",
    },
    {
      title: "Organisations",
      value: "5",
      description: "Organisations gérées",
      icon: Building2,
      color: "text-orange-600",
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Tableau de bord</h1>
        <p className="text-gray-600">
          Vue d'ensemble de votre système de gestion d'audit
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Activité récente</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Nouvel audit créé</p>
                  <p className="text-xs text-muted-foreground">
                    Audit de sécurité informatique - il y a 2 heures
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Rapport généré</p>
                  <p className="text-xs text-muted-foreground">
                    Rapport d'audit financier - il y a 4 heures
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Action assignée</p>
                  <p className="text-xs text-muted-foreground">
                    Correction de non-conformité - il y a 6 heures
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Actions en attente</CardTitle>
            <CardDescription>
              Actions nécessitant votre attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Validation rapport</p>
                  <p className="text-xs text-muted-foreground">
                    Échéance: Demain
                  </p>
                </div>
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Révision audit</p>
                  <p className="text-xs text-muted-foreground">
                    Échéance: Dans 3 jours
                  </p>
                </div>
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
