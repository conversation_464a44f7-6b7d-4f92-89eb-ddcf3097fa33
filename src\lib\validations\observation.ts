import { z } from "zod"

// Enum pour les sévérités d'observation
export const ObservationSeverity = {
  LOW: "LOW",
  MEDIUM: "MEDIUM", 
  HIGH: "HIGH",
  CRITICAL: "CRITICAL"
} as const

export type ObservationSeverityType = typeof ObservationSeverity[keyof typeof ObservationSeverity]

// Enum pour les statuts d'observation
export const ObservationStatus = {
  OPEN: "OPEN",
  IN_PROGRESS: "IN_PROGRESS",
  RESOLVED: "RESOLVED",
  CLOSED: "CLOSED",
  REJECTED: "REJECTED"
} as const

export type ObservationStatusType = typeof ObservationStatus[keyof typeof ObservationStatus]

// Schema de validation pour la création d'une observation
export const createObservationSchema = z.object({
  title: z.string().min(1, "Le titre est requis").max(200, "Le titre ne peut pas dépasser 200 caractères"),
  description: z.string().min(1, "La description est requise").max(2000, "La description ne peut pas dépasser 2000 caractères"),
  severity: z.enum([
    ObservationSeverity.LOW,
    ObservationSeverity.MEDIUM,
    ObservationSeverity.HIGH,
    ObservationSeverity.CRITICAL
  ]).default(ObservationSeverity.MEDIUM),
  evidence: z.string().optional(),
  auditId: z.string().min(1, "L'audit est requis")
})

// Schema de validation pour la mise à jour d'une observation
export const updateObservationSchema = z.object({
  title: z.string().min(1, "Le titre est requis").max(200, "Le titre ne peut pas dépasser 200 caractères").optional(),
  description: z.string().min(1, "La description est requise").max(2000, "La description ne peut pas dépasser 2000 caractères").optional(),
  severity: z.enum([
    ObservationSeverity.LOW,
    ObservationSeverity.MEDIUM,
    ObservationSeverity.HIGH,
    ObservationSeverity.CRITICAL
  ]).optional(),
  status: z.enum([
    ObservationStatus.OPEN,
    ObservationStatus.IN_PROGRESS,
    ObservationStatus.RESOLVED,
    ObservationStatus.CLOSED,
    ObservationStatus.REJECTED
  ]).optional(),
  evidence: z.string().optional()
})

// Schema pour les filtres de recherche
export const observationFiltersSchema = z.object({
  auditId: z.string().optional(),
  severity: z.enum([
    ObservationSeverity.LOW,
    ObservationSeverity.MEDIUM,
    ObservationSeverity.HIGH,
    ObservationSeverity.CRITICAL
  ]).optional(),
  status: z.enum([
    ObservationStatus.OPEN,
    ObservationStatus.IN_PROGRESS,
    ObservationStatus.RESOLVED,
    ObservationStatus.CLOSED,
    ObservationStatus.REJECTED
  ]).optional(),
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(["title", "severity", "status", "createdAt", "updatedAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
})

// Types TypeScript inférés
export type CreateObservationInput = z.infer<typeof createObservationSchema>
export type UpdateObservationInput = z.infer<typeof updateObservationSchema>
export type ObservationFilters = z.infer<typeof observationFiltersSchema>

// Type pour l'observation avec relations
export interface ObservationWithRelations {
  id: string
  title: string
  description: string
  severity: ObservationSeverityType
  status: ObservationStatusType
  evidence: string | null
  createdAt: Date
  updatedAt: Date
  auditId: string
  audit: {
    id: string
    title: string
    status: string
    organization: {
      id: string
      name: string
    }
  }
  actions: Array<{
    id: string
    title: string
    status: string
    dueDate: Date
    assignee: {
      id: string
      name: string | null
      email: string
    }
  }>
  _count: {
    actions: number
  }
}

// Utilitaires pour les sévérités
export const getObservationSeverityLabel = (severity: ObservationSeverityType): string => {
  const labels: Record<ObservationSeverityType, string> = {
    [ObservationSeverity.LOW]: "Faible",
    [ObservationSeverity.MEDIUM]: "Moyenne",
    [ObservationSeverity.HIGH]: "Élevée",
    [ObservationSeverity.CRITICAL]: "Critique"
  }
  return labels[severity]
}

export const getObservationSeverityColor = (severity: ObservationSeverityType): string => {
  const colors: Record<ObservationSeverityType, string> = {
    [ObservationSeverity.LOW]: "bg-blue-100 text-blue-800 border-blue-200",
    [ObservationSeverity.MEDIUM]: "bg-yellow-100 text-yellow-800 border-yellow-200",
    [ObservationSeverity.HIGH]: "bg-orange-100 text-orange-800 border-orange-200",
    [ObservationSeverity.CRITICAL]: "bg-red-100 text-red-800 border-red-200"
  }
  return colors[severity]
}

// Utilitaires pour les statuts
export const getObservationStatusLabel = (status: ObservationStatusType): string => {
  const labels: Record<ObservationStatusType, string> = {
    [ObservationStatus.OPEN]: "Ouverte",
    [ObservationStatus.IN_PROGRESS]: "En cours",
    [ObservationStatus.RESOLVED]: "Résolue",
    [ObservationStatus.CLOSED]: "Fermée",
    [ObservationStatus.REJECTED]: "Rejetée"
  }
  return labels[status]
}

export const getObservationStatusColor = (status: ObservationStatusType): string => {
  const colors: Record<ObservationStatusType, string> = {
    [ObservationStatus.OPEN]: "bg-red-100 text-red-800 border-red-200",
    [ObservationStatus.IN_PROGRESS]: "bg-yellow-100 text-yellow-800 border-yellow-200",
    [ObservationStatus.RESOLVED]: "bg-green-100 text-green-800 border-green-200",
    [ObservationStatus.CLOSED]: "bg-gray-100 text-gray-800 border-gray-200",
    [ObservationStatus.REJECTED]: "bg-purple-100 text-purple-800 border-purple-200"
  }
  return colors[status]
}

// Fonction pour calculer la priorité basée sur la sévérité
export const getObservationPriority = (severity: ObservationSeverityType): number => {
  const priorities: Record<ObservationSeverityType, number> = {
    [ObservationSeverity.CRITICAL]: 4,
    [ObservationSeverity.HIGH]: 3,
    [ObservationSeverity.MEDIUM]: 2,
    [ObservationSeverity.LOW]: 1
  }
  return priorities[severity]
}

// Fonction pour vérifier si une observation peut être modifiée
export const canEditObservation = (status: ObservationStatusType): boolean => {
  return status !== ObservationStatus.CLOSED && status !== ObservationStatus.REJECTED
}

// Fonction pour vérifier si une observation peut être supprimée
export const canDeleteObservation = (status: ObservationStatusType): boolean => {
  return status === ObservationStatus.OPEN
}

// Statistiques d'observations
export interface ObservationStats {
  total: number
  bySeverity: Record<ObservationSeverityType, number>
  byStatus: Record<ObservationStatusType, number>
  openCount: number
  criticalCount: number
  resolvedCount: number
}

// Fonction pour calculer les statistiques
export const calculateObservationStats = (observations: ObservationWithRelations[]): ObservationStats => {
  const stats: ObservationStats = {
    total: observations.length,
    bySeverity: {
      [ObservationSeverity.LOW]: 0,
      [ObservationSeverity.MEDIUM]: 0,
      [ObservationSeverity.HIGH]: 0,
      [ObservationSeverity.CRITICAL]: 0
    },
    byStatus: {
      [ObservationStatus.OPEN]: 0,
      [ObservationStatus.IN_PROGRESS]: 0,
      [ObservationStatus.RESOLVED]: 0,
      [ObservationStatus.CLOSED]: 0,
      [ObservationStatus.REJECTED]: 0
    },
    openCount: 0,
    criticalCount: 0,
    resolvedCount: 0
  }

  observations.forEach(obs => {
    stats.bySeverity[obs.severity]++
    stats.byStatus[obs.status]++
    
    if (obs.status === ObservationStatus.OPEN) {
      stats.openCount++
    }
    if (obs.severity === ObservationSeverity.CRITICAL) {
      stats.criticalCount++
    }
    if (obs.status === ObservationStatus.RESOLVED) {
      stats.resolvedCount++
    }
  })

  return stats
}
