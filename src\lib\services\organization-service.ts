// Service temporaire pour les organisations (mock data)
// En attendant l'implémentation complète avec Prisma

export interface Organization {
  id: string
  name: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// Mock data pour les organisations
const mockOrganizations: Organization[] = [
  {
    id: "org_1",
    name: "Organisation Principale",
    description: "Organisation principale du système",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "org_2", 
    name: "Filiale Nord",
    description: "Filiale située dans le nord",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: "org_3",
    name: "Filiale Sud", 
    description: "Filiale située dans le sud",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
]

export class OrganizationService {
  static async getOrganizations(userRole?: string, userOrgId?: string): Promise<Organization[]> {
    // Si l'utilisateur n'est pas admin, ne retourner que son organisation
    if (userRole !== "SUPER_ADMIN" && userRole !== "ADMIN" && userOrgId) {
      return mockOrganizations.filter(org => org.id === userOrgId)
    }
    
    return mockOrganizations
  }

  static async getOrganizationById(id: string): Promise<Organization | null> {
    return mockOrganizations.find(org => org.id === id) || null
  }

  static async createOrganization(data: Omit<Organization, 'id' | 'createdAt' | 'updatedAt'>): Promise<Organization> {
    const newOrganization: Organization = {
      id: `org_${Date.now()}`,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    mockOrganizations.push(newOrganization)
    return newOrganization
  }

  static async updateOrganization(id: string, data: Partial<Omit<Organization, 'id' | 'createdAt'>>): Promise<Organization | null> {
    const index = mockOrganizations.findIndex(org => org.id === id)
    
    if (index === -1) {
      return null
    }

    mockOrganizations[index] = {
      ...mockOrganizations[index],
      ...data,
      updatedAt: new Date().toISOString(),
    }

    return mockOrganizations[index]
  }

  static async deleteOrganization(id: string): Promise<boolean> {
    const index = mockOrganizations.findIndex(org => org.id === id)
    
    if (index === -1) {
      return false
    }

    mockOrganizations.splice(index, 1)
    return true
  }
}
