import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { AuditService } from "@/lib/services/audit-service"
import { createAuditSchema, auditFiltersSchema } from "@/lib/validations/audit"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * GET /api/audits - Obtenir la liste des audits
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'audits', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Parser les paramètres de requête
      const { searchParams } = new URL(req.url)
      const filters = auditFiltersSchema.parse({
        status: searchParams.get('status') || undefined,
        organizationId: searchParams.get('organizationId') || undefined,
        auditorId: searchParams.get('auditorId') || undefined,
        startDateFrom: searchParams.get('startDateFrom') || undefined,
        startDateTo: searchParams.get('startDateTo') || undefined,
        search: searchParams.get('search') || undefined,
        page: searchParams.get('page') || 1,
        limit: searchParams.get('limit') || 10,
        sortBy: searchParams.get('sortBy') || 'createdAt',
        sortOrder: searchParams.get('sortOrder') || 'desc'
      })

      // Si l'utilisateur n'est pas admin, filtrer par son organisation
      if (user.role !== "SUPER_ADMIN" && user.organizationId) {
        filters.organizationId = user.organizationId
      }

      // Si l'utilisateur est un auditeur simple, ne voir que ses audits
      if (user.role === "AUDITOR") {
        filters.auditorId = user.id
      }

      const result = await AuditService.getAudits(filters)

      return NextResponse.json({
        success: true,
        data: result
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des audits:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Paramètres invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * POST /api/audits - Créer un nouvel audit
 */
export async function POST(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'audits', 'create')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = createAuditSchema.parse(body)

      // Si l'utilisateur n'est pas admin, forcer son organisation
      if (user.role !== "SUPER_ADMIN" && user.organizationId) {
        validatedData.organizationId = user.organizationId
      }

      const audit = await AuditService.createAudit(validatedData, user.id)

      return NextResponse.json({
        success: true,
        data: audit
      }, { status: 201 })

    } catch (error) {
      console.error("Erreur lors de la création de l'audit:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
