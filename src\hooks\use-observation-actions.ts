"use client"

import { useState } from "react"
import { CreateObservationInput, UpdateObservationInput, ObservationWithRelations } from "@/lib/validations/observation"

interface UseObservationActionsResult {
  loading: boolean
  error: string | null
  createObservation: (data: CreateObservationInput) => Promise<ObservationWithRelations | null>
  updateObservation: (id: string, data: UpdateObservationInput) => Promise<ObservationWithRelations | null>
  deleteObservation: (id: string) => Promise<boolean>
  getObservation: (id: string) => Promise<ObservationWithRelations | null>
  resolveObservation: (id: string) => Promise<ObservationWithRelations | null>
  closeObservation: (id: string) => Promise<ObservationWithRelations | null>
  reopenObservation: (id: string) => Promise<ObservationWithRelations | null>
  clearError: () => void
}

export function useObservationActions(): UseObservationActionsResult {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearError = () => setError(null)

  const createObservation = async (data: CreateObservationInput): Promise<ObservationWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/observations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la création de l'observation")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la création de l'observation")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la création de l'observation:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const updateObservation = async (id: string, data: UpdateObservationInput): Promise<ObservationWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/observations/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la mise à jour de l'observation")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la mise à jour de l'observation")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la mise à jour de l'observation:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const deleteObservation = async (id: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/observations/${id}`, {
        method: "DELETE",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la suppression de l'observation")
      }

      return result.success
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la suppression de l'observation:", err)
      return false
    } finally {
      setLoading(false)
    }
  }

  const getObservation = async (id: string): Promise<ObservationWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/observations/${id}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la récupération de l'observation")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la récupération de l'observation")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la récupération de l'observation:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const resolveObservation = async (id: string): Promise<ObservationWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/observations/${id}/resolve`, {
        method: "POST",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la résolution de l'observation")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la résolution de l'observation")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la résolution de l'observation:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const closeObservation = async (id: string): Promise<ObservationWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/observations/${id}/close`, {
        method: "POST",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la fermeture de l'observation")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la fermeture de l'observation")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la fermeture de l'observation:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const reopenObservation = async (id: string): Promise<ObservationWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/observations/${id}/reopen`, {
        method: "POST",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la réouverture de l'observation")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la réouverture de l'observation")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la réouverture de l'observation:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    error,
    createObservation,
    updateObservation,
    deleteObservation,
    getObservation,
    resolveObservation,
    closeObservation,
    reopenObservation,
    clearError
  }
}
