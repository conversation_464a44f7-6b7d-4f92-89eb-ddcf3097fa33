import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { AuditService } from "@/lib/services/audit-service"
import { hasPermission, UserRole } from "@/lib/validations/user"

/**
 * GET /api/audits/stats - Obtenir les statistiques des audits
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'audits', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Déterminer l'organisation à filtrer selon le rôle
      let organizationId: string | undefined

      if (user.role !== UserRole.SUPER_ADMIN && user.organizationId) {
        organizationId = user.organizationId
      }

      // Permettre de filtrer par organisation via query param pour les super admins
      const { searchParams } = new URL(req.url)
      const requestedOrgId = searchParams.get('organizationId')
      
      if (user.role === UserRole.SUPER_ADMIN && requestedOrgId) {
        organizationId = requestedOrgId
      }

      const stats = await AuditService.getAuditStats(organizationId)

      return NextResponse.json({
        success: true,
        data: stats
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des statistiques:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
