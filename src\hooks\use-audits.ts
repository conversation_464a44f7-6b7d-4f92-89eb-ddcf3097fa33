"use client"

import { useState, useEffect, useCallback } from "react"
import { AuditFilters, AuditWithRelations } from "@/lib/validations/audit"

interface UseAuditsResult {
  audits: AuditWithRelations[]
  total: number
  totalPages: number
  loading: boolean
  error: string | null
  filters: AuditFilters
  setFilters: (filters: AuditFilters) => void
  refetch: () => Promise<void>
}

const defaultFilters: AuditFilters = {
  page: 1,
  limit: 10,
  sortBy: "createdAt",
  sortOrder: "desc"
}

export function useAudits(initialFilters?: Partial<AuditFilters>): UseAuditsResult {
  const [audits, setAudits] = useState<AuditWithRelations[]>([])
  const [total, setTotal] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<AuditFilters>({
    ...defaultFilters,
    ...initialFilters
  })

  const fetchAudits = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Construire les paramètres de requête
      const searchParams = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams.append(key, value.toString())
        }
      })

      const response = await fetch(`/api/audits?${searchParams.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erreur lors du chargement des audits")
      }

      const data = await response.json()
      
      if (data.success) {
        setAudits(data.data.audits)
        setTotal(data.data.total)
        setTotalPages(data.data.totalPages)
      } else {
        throw new Error(data.error || "Erreur lors du chargement des audits")
      }
    } catch (err) {
      console.error("Erreur lors du chargement des audits:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
      setAudits([])
      setTotal(0)
      setTotalPages(0)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Charger les audits quand les filtres changent
  useEffect(() => {
    fetchAudits()
  }, [fetchAudits])

  const updateFilters = useCallback((newFilters: AuditFilters) => {
    setFilters(newFilters)
  }, [])

  return {
    audits,
    total,
    totalPages,
    loading,
    error,
    filters,
    setFilters: updateFilters,
    refetch: fetchAudits
  }
}
