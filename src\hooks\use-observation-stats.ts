"use client"

import { useState, useEffect } from "react"
import { ObservationStats } from "@/lib/validations/observation"

interface UseObservationStatsResult {
  stats: ObservationStats | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useObservationStats(auditId?: string): UseObservationStatsResult {
  const [stats, setStats] = useState<ObservationStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)

      const searchParams = new URLSearchParams()
      if (auditId) {
        searchParams.append('auditId', auditId)
      }

      const response = await fetch(`/api/observations/stats?${searchParams.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erreur lors du chargement des statistiques")
      }

      const data = await response.json()
      
      if (data.success) {
        setStats(data.data)
      } else {
        throw new Error(data.error || "Erreur lors du chargement des statistiques")
      }
    } catch (err) {
      console.error("Erreur lors du chargement des statistiques:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
      setStats(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [auditId])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  }
}
