import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { AuditService } from "@/lib/services/audit-service"
import { assignAuditorsSchema } from "@/lib/validations/audit"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * PUT /api/audits/[id]/auditors - Assigner des auditeurs à un audit
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'audits', 'update')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'audit existe et que l'utilisateur peut le modifier
      const existingAudit = await AuditService.getAuditById(params.id)
      
      if (!existingAudit) {
        return NextResponse.json(
          { success: false, error: "Audit non trouvé" },
          { status: 404 }
        )
      }

      const canUpdate = 
        user.role === UserRole.SUPER_ADMIN ||
        user.role === UserRole.MANAGER ||
        (existingAudit.organizationId === user.organizationId && 
         (existingAudit.creatorId === user.id || 
          existingAudit.auditors.some(a => a.user.id === user.id && a.role === 'LEAD_AUDITOR')))

      if (!canUpdate) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé pour modifier les auditeurs de cet audit" },
          { status: 403 }
        )
      }

      // Empêcher la modification des auditeurs pour les audits terminés
      if (existingAudit.status === 'COMPLETED') {
        return NextResponse.json(
          { success: false, error: "Impossible de modifier les auditeurs d'un audit terminé" },
          { status: 400 }
        )
      }

      const body = await req.json()
      const validatedData = assignAuditorsSchema.parse(body)

      const updatedAudit = await AuditService.assignAuditors(params.id, validatedData)

      return NextResponse.json({
        success: true,
        data: updatedAudit,
        message: "Auditeurs assignés avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de l'assignation des auditeurs:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
