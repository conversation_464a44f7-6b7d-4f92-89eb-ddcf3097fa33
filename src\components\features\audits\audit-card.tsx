import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { AuditStatusBadge } from "./audit-status-badge"
import { AuditObservationsSummary } from "./audit-observations-summary"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Calendar, Users, FileText, AlertTriangle, Eye, Edit, Trash2 } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface AuditCardProps {
  audit: AuditWithRelations
  onView?: (audit: AuditWithRelations) => void
  onEdit?: (audit: AuditWithRelations) => void
  onDelete?: (audit: AuditWithRelations) => void
  showActions?: boolean
}

export function AuditCard({ 
  audit, 
  onView, 
  onEdit, 
  onDelete, 
  showActions = true 
}: AuditCardProps) {
  const formatDate = (date: Date) => {
    return format(date, "dd MMM yyyy", { locale: fr })
  }

  const leadAuditor = audit.auditors.find(a => a.role === "LEAD_AUDITOR")

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold text-gray-900">
              {audit.title}
            </CardTitle>
            <CardDescription className="text-sm text-gray-600">
              {audit.organization.name}
            </CardDescription>
          </div>
          <AuditStatusBadge status={audit.status} />
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Description */}
        {audit.description && (
          <p className="text-sm text-gray-700 line-clamp-2">
            {audit.description}
          </p>
        )}

        {/* Informations principales */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-gray-700">
              {formatDate(audit.startDate)}
              {audit.endDate && ` - ${formatDate(audit.endDate)}`}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-500" />
            <span className="text-gray-700">
              {audit.auditors.length} auditeur{audit.auditors.length > 1 ? 's' : ''}
            </span>
          </div>
        </div>

        {/* Auditeur principal */}
        {leadAuditor && (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              Auditeur principal
            </Badge>
            <span className="text-sm text-gray-700">
              {leadAuditor.user.name || leadAuditor.user.email}
            </span>
          </div>
        )}

        {/* Statistiques */}
        <div className="space-y-2">
          <div className="flex items-center gap-4 text-xs text-gray-600">
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              <span>{audit._count.actions} action{audit._count.actions > 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              <span>{audit._count.reports} rapport{audit._count.reports > 1 ? 's' : ''}</span>
            </div>
          </div>

          {/* Résumé des observations */}
          <AuditObservationsSummary auditId={audit.id} />
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex items-center gap-2 pt-2 border-t">
            {onView && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onView(audit)}
                className="flex items-center gap-1"
              >
                <Eye className="h-3 w-3" />
                Voir
              </Button>
            )}
            
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(audit)}
                className="flex items-center gap-1"
              >
                <Edit className="h-3 w-3" />
                Modifier
              </Button>
            )}
            
            {onDelete && audit.status === 'PLANNED' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(audit)}
                className="flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-3 w-3" />
                Supprimer
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
