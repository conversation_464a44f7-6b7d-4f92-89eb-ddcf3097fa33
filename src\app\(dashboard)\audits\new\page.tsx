"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { AuditForm } from "@/components/features/audits/audit-form"
import { useAuditActions } from "@/hooks/use-audit-actions"
import { useFormData } from "@/hooks/use-form-data"
import { CreateAuditInput } from "@/lib/validations/audit"
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"

export default function NewAuditPage() {
  const router = useRouter()

  const {
    loading: actionLoading,
    error: actionError,
    createAudit,
    clearError
  } = useAuditActions()

  const {
    organizations,
    users,
    loading: dataLoading,
    error: dataError
  } = useFormData()

  const handleSubmit = async (data: CreateAuditInput) => {
    const audit = await createAudit(data)
    if (audit) {
      router.push(`/audits/${audit.id}`)
    }
  }

  if (dataLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" disabled>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Nouvel audit</h1>
            <p className="text-gray-600">Chargement...</p>
          </div>
        </div>

        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (dataError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Nouvel audit</h1>
            <p className="text-gray-600">Erreur lors du chargement</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{dataError}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>

        <div>
          <h1 className="text-3xl font-bold magneto-title">Nouvel audit</h1>
          <p className="text-gray-600">
            Créez un nouvel audit et assignez les auditeurs
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <AuditForm
        organizations={organizations}
        users={users}
        onSubmit={handleSubmit}
        loading={actionLoading}
        error={actionError}
      />
    </div>
  )
}
