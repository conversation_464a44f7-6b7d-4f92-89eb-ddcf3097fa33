"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useSimpleAuth, useSimplePermissions } from "@/lib/auth/simple-auth"
import { ROLE_LABELS } from "@/lib/validations/user"
import { useRouter } from "next/navigation"

export default function TestAuthSimplePage() {
  const { user, isAuthenticated, signOut } = useSimpleAuth()
  const permissions = useSimplePermissions()
  const router = useRouter()

  const handleSignOut = () => {
    signOut()
    router.push("/login-simple")
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold magneto-title">Test Auth Simple</h1>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">Non connecté</p>
              <Button onClick={() => router.push("/login-simple")}>
                Aller à la connexion
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold magneto-title">Test Auth Simple</h1>
          <p className="text-gray-600">
            Test du système d'authentification simple
          </p>
        </div>
        <Button onClick={handleSignOut} variant="outline">
          Se déconnecter
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Info */}
        <Card>
          <CardHeader>
            <CardTitle>Informations utilisateur</CardTitle>
            <CardDescription>Données de l'utilisateur connecté</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Statut</label>
              <div className="mt-1">
                <Badge variant="default">✓ Connecté</Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Nom</label>
              <p className="text-lg">{user.name || "Non renseigné"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p className="text-lg">{user.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Rôle</label>
              <div className="mt-1">
                <Badge variant="outline">
                  {ROLE_LABELS[user.role as keyof typeof ROLE_LABELS] || user.role}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Statut du compte</label>
              <div className="mt-1">
                <Badge variant={user.isActive ? "default" : "secondary"}>
                  {user.isActive ? "Actif" : "Inactif"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions</CardTitle>
            <CardDescription>Permissions accordées à l'utilisateur</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="flex justify-between">
                <span>Gérer utilisateurs:</span>
                <Badge variant={permissions.canManageUsers ? "default" : "secondary"}>
                  {permissions.canManageUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Créer utilisateurs:</span>
                <Badge variant={permissions.canCreateUsers ? "default" : "secondary"}>
                  {permissions.canCreateUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Modifier utilisateurs:</span>
                <Badge variant={permissions.canUpdateUsers ? "default" : "secondary"}>
                  {permissions.canUpdateUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Supprimer utilisateurs:</span>
                <Badge variant={permissions.canDeleteUsers ? "default" : "secondary"}>
                  {permissions.canDeleteUsers ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Gérer audits:</span>
                <Badge variant={permissions.canManageAudits ? "default" : "secondary"}>
                  {permissions.canManageAudits ? "✓" : "✗"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Gérer rapports:</span>
                <Badge variant={permissions.canManageReports ? "default" : "secondary"}>
                  {permissions.canManageReports ? "✓" : "✗"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions de test */}
      <Card>
        <CardHeader>
          <CardTitle>Actions de test</CardTitle>
          <CardDescription>Testez les fonctionnalités avec cet utilisateur</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={() => router.push("/admin/users")}
              disabled={!permissions.canManageUsers}
              className="magneto-button"
            >
              Gestion des utilisateurs
            </Button>
            <Button 
              onClick={() => router.push("/test-users")}
              className="magneto-button"
            >
              Test API utilisateurs
            </Button>
            <Button 
              onClick={() => router.push("/audits")}
              disabled={!permissions.canManageAudits}
              className="magneto-button"
            >
              Gestion des audits
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de débogage</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-50 p-4 rounded-lg text-xs overflow-auto">
            {JSON.stringify({ user, permissions }, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}
