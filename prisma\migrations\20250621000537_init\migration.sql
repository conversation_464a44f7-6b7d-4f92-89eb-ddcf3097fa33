BEGIN TRY

BEGIN TRAN;

-- CreateTable
CREATE TABLE [dbo].[users] (
    [id] NVARCHAR(1000) NOT NULL,
    [email] NVARCHAR(1000) NOT NULL,
    [name] NVARCHAR(1000),
    [password] NVARCHAR(1000),
    [role] NVARCHAR(1000) NOT NULL CONSTRAINT [users_role_df] DEFAULT 'USER',
    [isActive] BIT NOT NULL CONSTRAINT [users_isActive_df] DEFAULT 1,
    [createdAt] DATETIME2 NOT NULL CONSTRAINT [users_createdAt_df] DEFAULT CURRENT_TIMESTAMP,
    [updatedAt] DATETIME2 NOT NULL,
    [lastLoginAt] DATETIME2,
    [organizationId] NVARCHAR(1000),
    CONSTRAINT [users_pkey] PRIMARY KEY CLUSTERED ([id]),
    CONSTRAINT [users_email_key] UNIQUE NONCLUSTERED ([email])
);

-- CreateTable
CREATE TABLE [dbo].[organizations] (
    [id] NVARCHAR(1000) NOT NULL,
    [name] NVARCHAR(1000) NOT NULL,
    [description] NVARCHAR(1000),
    [isActive] BIT NOT NULL CONSTRAINT [organizations_isActive_df] DEFAULT 1,
    [createdAt] DATETIME2 NOT NULL CONSTRAINT [organizations_createdAt_df] DEFAULT CURRENT_TIMESTAMP,
    [updatedAt] DATETIME2 NOT NULL,
    CONSTRAINT [organizations_pkey] PRIMARY KEY CLUSTERED ([id])
);

-- CreateTable
CREATE TABLE [dbo].[audits] (
    [id] NVARCHAR(1000) NOT NULL,
    [title] NVARCHAR(1000) NOT NULL,
    [description] NVARCHAR(1000),
    [status] NVARCHAR(1000) NOT NULL CONSTRAINT [audits_status_df] DEFAULT 'PLANNED',
    [startDate] DATETIME2 NOT NULL,
    [endDate] DATETIME2,
    [createdAt] DATETIME2 NOT NULL CONSTRAINT [audits_createdAt_df] DEFAULT CURRENT_TIMESTAMP,
    [updatedAt] DATETIME2 NOT NULL,
    [organizationId] NVARCHAR(1000) NOT NULL,
    [creatorId] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [audits_pkey] PRIMARY KEY CLUSTERED ([id])
);

-- CreateTable
CREATE TABLE [dbo].[audit_users] (
    [id] NVARCHAR(1000) NOT NULL,
    [role] NVARCHAR(1000) NOT NULL CONSTRAINT [audit_users_role_df] DEFAULT 'AUDITOR',
    [auditId] NVARCHAR(1000) NOT NULL,
    [userId] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [audit_users_pkey] PRIMARY KEY CLUSTERED ([id]),
    CONSTRAINT [audit_users_auditId_userId_key] UNIQUE NONCLUSTERED ([auditId],[userId])
);

-- CreateTable
CREATE TABLE [dbo].[observations] (
    [id] NVARCHAR(1000) NOT NULL,
    [title] NVARCHAR(1000) NOT NULL,
    [description] NVARCHAR(1000) NOT NULL,
    [severity] NVARCHAR(1000) NOT NULL CONSTRAINT [observations_severity_df] DEFAULT 'LOW',
    [status] NVARCHAR(1000) NOT NULL CONSTRAINT [observations_status_df] DEFAULT 'OPEN',
    [evidence] NVARCHAR(1000),
    [createdAt] DATETIME2 NOT NULL CONSTRAINT [observations_createdAt_df] DEFAULT CURRENT_TIMESTAMP,
    [updatedAt] DATETIME2 NOT NULL,
    [auditId] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [observations_pkey] PRIMARY KEY CLUSTERED ([id])
);

-- CreateTable
CREATE TABLE [dbo].[actions] (
    [id] NVARCHAR(1000) NOT NULL,
    [title] NVARCHAR(1000) NOT NULL,
    [description] NVARCHAR(1000) NOT NULL,
    [dueDate] DATETIME2 NOT NULL,
    [status] NVARCHAR(1000) NOT NULL CONSTRAINT [actions_status_df] DEFAULT 'PENDING',
    [priority] NVARCHAR(1000) NOT NULL CONSTRAINT [actions_priority_df] DEFAULT 'MEDIUM',
    [createdAt] DATETIME2 NOT NULL CONSTRAINT [actions_createdAt_df] DEFAULT CURRENT_TIMESTAMP,
    [updatedAt] DATETIME2 NOT NULL,
    [completedAt] DATETIME2,
    [auditId] NVARCHAR(1000) NOT NULL,
    [observationId] NVARCHAR(1000),
    [assigneeId] NVARCHAR(1000) NOT NULL,
    [creatorId] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [actions_pkey] PRIMARY KEY CLUSTERED ([id])
);

-- CreateTable
CREATE TABLE [dbo].[reports] (
    [id] NVARCHAR(1000) NOT NULL,
    [title] NVARCHAR(1000) NOT NULL,
    [content] NVARCHAR(1000) NOT NULL,
    [status] NVARCHAR(1000) NOT NULL CONSTRAINT [reports_status_df] DEFAULT 'DRAFT',
    [version] INT NOT NULL CONSTRAINT [reports_version_df] DEFAULT 1,
    [createdAt] DATETIME2 NOT NULL CONSTRAINT [reports_createdAt_df] DEFAULT CURRENT_TIMESTAMP,
    [updatedAt] DATETIME2 NOT NULL,
    [publishedAt] DATETIME2,
    [organizationId] NVARCHAR(1000) NOT NULL,
    [auditId] NVARCHAR(1000) NOT NULL,
    [creatorId] NVARCHAR(1000) NOT NULL,
    CONSTRAINT [reports_pkey] PRIMARY KEY CLUSTERED ([id])
);

-- AddForeignKey
ALTER TABLE [dbo].[users] ADD CONSTRAINT [users_organizationId_fkey] FOREIGN KEY ([organizationId]) REFERENCES [dbo].[organizations]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[audits] ADD CONSTRAINT [audits_organizationId_fkey] FOREIGN KEY ([organizationId]) REFERENCES [dbo].[organizations]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[audits] ADD CONSTRAINT [audits_creatorId_fkey] FOREIGN KEY ([creatorId]) REFERENCES [dbo].[users]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[audit_users] ADD CONSTRAINT [audit_users_auditId_fkey] FOREIGN KEY ([auditId]) REFERENCES [dbo].[audits]([id]) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[audit_users] ADD CONSTRAINT [audit_users_userId_fkey] FOREIGN KEY ([userId]) REFERENCES [dbo].[users]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[observations] ADD CONSTRAINT [observations_auditId_fkey] FOREIGN KEY ([auditId]) REFERENCES [dbo].[audits]([id]) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE [dbo].[actions] ADD CONSTRAINT [actions_auditId_fkey] FOREIGN KEY ([auditId]) REFERENCES [dbo].[audits]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[actions] ADD CONSTRAINT [actions_observationId_fkey] FOREIGN KEY ([observationId]) REFERENCES [dbo].[observations]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[actions] ADD CONSTRAINT [actions_assigneeId_fkey] FOREIGN KEY ([assigneeId]) REFERENCES [dbo].[users]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[actions] ADD CONSTRAINT [actions_creatorId_fkey] FOREIGN KEY ([creatorId]) REFERENCES [dbo].[users]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[reports] ADD CONSTRAINT [reports_organizationId_fkey] FOREIGN KEY ([organizationId]) REFERENCES [dbo].[organizations]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[reports] ADD CONSTRAINT [reports_auditId_fkey] FOREIGN KEY ([auditId]) REFERENCES [dbo].[audits]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE [dbo].[reports] ADD CONSTRAINT [reports_creatorId_fkey] FOREIGN KEY ([creatorId]) REFERENCES [dbo].[users]([id]) ON DELETE NO ACTION ON UPDATE NO ACTION;

COMMIT TRAN;

END TRY
BEGIN CATCH

IF @@TRANCOUNT > 0
BEGIN
    ROLLBACK TRAN;
END;
THROW

END CATCH
