"use client"

import { useSimplePermissions } from "@/lib/auth/simple-auth"

// Hook pour vérifier les permissions d'audit
export function useAuditPermissions(audit?: any) {
  const permissions = useSimplePermissions()

  return {
    canRead: () => permissions.checkPermission("audits:read"),
    canCreate: () => permissions.checkPermission("audits:create"),
    canUpdate: () => permissions.checkPermission("audits:update"),
    canDelete: () => permissions.checkPermission("audits:delete"),
    canAssignAuditors: () => permissions.checkPermission("audits:update"),
    canCreateObservation: () => permissions.checkPermission("observations:create"),
    canCreateAction: () => permissions.checkPermission("audits:update"),
    canGenerateReport: () => permissions.checkPermission("reports:create"),
    user: null // Temporaire
  }
}
