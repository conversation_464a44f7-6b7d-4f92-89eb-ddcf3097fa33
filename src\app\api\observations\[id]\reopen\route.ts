import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ObservationService } from "@/lib/services/observation-service"
import { hasPermission, UserRole } from "@/lib/validations/user"

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * POST /api/observations/[id]/reopen - Rouvrir une observation
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'observations', 'update')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'observation existe
      const existingObservation = await ObservationService.getObservationById(params.id)
      
      if (!existingObservation) {
        return NextResponse.json(
          { success: false, error: "Observation non trouvée" },
          { status: 404 }
        )
      }

      // Vérifier que l'observation peut être rouverte
      if (existingObservation.status === 'OPEN') {
        return NextResponse.json(
          { success: false, error: "Cette observation est déjà ouverte" },
          { status: 400 }
        )
      }

      if (existingObservation.status === 'REJECTED') {
        return NextResponse.json(
          { success: false, error: "Une observation rejetée ne peut pas être rouverte" },
          { status: 400 }
        )
      }

      const reopenedObservation = await ObservationService.reopenObservation(params.id)

      return NextResponse.json({
        success: true,
        data: reopenedObservation,
        message: "Observation rouverte avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de la réouverture de l'observation:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
