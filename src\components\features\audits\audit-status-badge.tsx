import { Badge } from "@/components/ui/badge"
import { AuditStatusType, getAuditStatusLabel, getAuditStatusColor } from "@/lib/validations/audit"

interface AuditStatusBadgeProps {
  status: AuditStatusType
  className?: string
}

export function AuditStatusBadge({ status, className }: AuditStatusBadgeProps) {
  return (
    <Badge 
      variant="secondary" 
      className={`${getAuditStatusColor(status)} ${className}`}
    >
      {getAuditStatusLabel(status)}
    </Badge>
  )
}
