"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { authClient, type Session, type User } from "@/lib/auth/client"

interface AuthContextType {
  session: Session | null
  user: User | null
  isLoading: boolean
  signIn: typeof authClient.signIn
  signUp: typeof authClient.signUp
  signOut: typeof authClient.signOut
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const refreshSession = async () => {
    try {
      const sessionData = await authClient.getSession()
      setSession(sessionData.data)
    } catch (error) {
      console.error("Failed to get session:", error)
      setSession(null)
    }
  }

  useEffect(() => {
    const getSession = async () => {
      try {
        const sessionData = await authClient.getSession()
        setSession(sessionData.data)
      } catch (error) {
        console.error("Failed to get session:", error)
      } finally {
        setIsLoading(false)
      }
    }

    getSession()

    // Listen for storage events to refresh session when user signs in/out in another tab
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'better-auth.session') {
        refreshSession()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // Wrap signIn to refresh session after successful sign in
  const wrappedSignIn = {
    ...authClient.signIn,
    email: async (data: any) => {
      const result = await authClient.signIn.email(data)
      if (!result.error) {
        await refreshSession()
      }
      return result
    }
  }

  // Wrap signOut to clear session after sign out
  const wrappedSignOut = async () => {
    await authClient.signOut()
    setSession(null)
  }

  const value: AuthContextType = {
    session,
    user: session?.user || null,
    isLoading,
    signIn: wrappedSignIn,
    signUp: authClient.signUp,
    signOut: wrappedSignOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
