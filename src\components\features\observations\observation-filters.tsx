"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  ObservationFilters, 
  ObservationSeverity, 
  ObservationStatus 
} from "@/lib/validations/observation"
import { Search, Filter, X } from "lucide-react"

interface ObservationFiltersProps {
  filters: ObservationFilters
  onFiltersChange: (filters: ObservationFilters) => void
  audits?: Array<{ id: string; title: string }>
  loading?: boolean
}

export function ObservationFiltersComponent({ 
  filters, 
  onFiltersChange, 
  audits = [],
  loading = false 
}: ObservationFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const updateFilter = (key: keyof ObservationFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1 // Reset to first page when filtering
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      page: 1,
      limit: filters.limit,
      sortBy: "createdAt",
      sortOrder: "desc"
    })
  }

  const hasActiveFilters = !!(
    filters.search ||
    filters.severity ||
    filters.status ||
    filters.auditId
  )

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="text-red-600 hover:text-red-700"
              >
                <X className="h-4 w-4 mr-1" />
                Effacer
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? "Réduire" : "Étendre"}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Recherche - toujours visible */}
        <div className="space-y-2">
          <Label htmlFor="search">Recherche</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="search"
              placeholder="Rechercher par titre ou description..."
              value={filters.search || ""}
              onChange={(e) => updateFilter("search", e.target.value || undefined)}
              className="pl-10"
              disabled={loading}
            />
          </div>
        </div>

        {/* Filtres étendus */}
        {isExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Sévérité */}
            <div className="space-y-2">
              <Label>Sévérité</Label>
              <Select
                value={filters.severity || ""}
                onValueChange={(value) => updateFilter("severity", value || undefined)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les sévérités" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Toutes les sévérités</SelectItem>
                  <SelectItem value={ObservationSeverity.CRITICAL}>Critique</SelectItem>
                  <SelectItem value={ObservationSeverity.HIGH}>Élevée</SelectItem>
                  <SelectItem value={ObservationSeverity.MEDIUM}>Moyenne</SelectItem>
                  <SelectItem value={ObservationSeverity.LOW}>Faible</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Statut */}
            <div className="space-y-2">
              <Label>Statut</Label>
              <Select
                value={filters.status || ""}
                onValueChange={(value) => updateFilter("status", value || undefined)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Tous les statuts</SelectItem>
                  <SelectItem value={ObservationStatus.OPEN}>Ouverte</SelectItem>
                  <SelectItem value={ObservationStatus.IN_PROGRESS}>En cours</SelectItem>
                  <SelectItem value={ObservationStatus.RESOLVED}>Résolue</SelectItem>
                  <SelectItem value={ObservationStatus.CLOSED}>Fermée</SelectItem>
                  <SelectItem value={ObservationStatus.REJECTED}>Rejetée</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Audit */}
            {audits.length > 0 && (
              <div className="space-y-2">
                <Label>Audit</Label>
                <Select
                  value={filters.auditId || ""}
                  onValueChange={(value) => updateFilter("auditId", value || undefined)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tous les audits" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tous les audits</SelectItem>
                    {audits.map((audit) => (
                      <SelectItem key={audit.id} value={audit.id}>
                        {audit.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Tri */}
            <div className="space-y-2">
              <Label>Trier par</Label>
              <div className="flex gap-2">
                <Select
                  value={filters.sortBy}
                  onValueChange={(value) => updateFilter("sortBy", value)}
                  disabled={loading}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="title">Titre</SelectItem>
                    <SelectItem value="severity">Sévérité</SelectItem>
                    <SelectItem value="status">Statut</SelectItem>
                    <SelectItem value="createdAt">Date de création</SelectItem>
                    <SelectItem value="updatedAt">Date de modification</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select
                  value={filters.sortOrder}
                  onValueChange={(value) => updateFilter("sortOrder", value)}
                  disabled={loading}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Croissant</SelectItem>
                    <SelectItem value="desc">Décroissant</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
