"use client"

import { ReactNode } from "react"
import { useSession } from "@/lib/auth/client"
import { hasPermission } from "@/lib/validations/user"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Lock } from "lucide-react"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

interface AuditPermissionGuardProps {
  children: ReactNode
  permission: string
  fallback?: ReactNode
}

export function AuditPermissionGuard({ 
  children, 
  permission, 
  fallback 
}: AuditPermissionGuardProps) {
  const { data: session } = useSession()
  
  if (!session?.user) {
    return fallback || (
      <Alert>
        <Lock className="h-4 w-4" />
        <AlertDescription>
          Vous devez être connecté pour accéder à cette fonctionnalité.
        </AlertDescription>
      </Alert>
    )
  }

  const [resource, action] = permission.split(':')
  const userRole = session.user.role as UserRole
  
  if (!hasPermission(userRole, resource, action)) {
    return fallback || (
      <Alert>
        <Lock className="h-4 w-4" />
        <AlertDescription>
          Vous n'avez pas les permissions nécessaires pour accéder à cette fonctionnalité.
        </AlertDescription>
      </Alert>
    )
  }

  return <>{children}</>
}

// Hook pour vérifier les permissions d'audit
export function useAuditPermissions(audit?: any) {
  const { data: session } = useSession()
  
  const checkPermission = (permission: string): boolean => {
    if (!session?.user) return false
    
    const [resource, action] = permission.split(':')
    const userRole = session.user.role as UserRole
    
    return hasPermission(userRole, resource, action)
  }

  return {
    canRead: () => checkPermission("audits:read"),
    canCreate: () => checkPermission("audits:create"),
    canUpdate: () => checkPermission("audits:update"),
    canDelete: () => checkPermission("audits:delete"),
    canAssignAuditors: () => checkPermission("audits:update"),
    canCreateObservation: () => checkPermission("observations:create"),
    canCreateAction: () => checkPermission("audits:update"),
    canGenerateReport: () => checkPermission("reports:create"),
    user: session?.user || null
  }
}
