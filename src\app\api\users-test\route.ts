import { NextRequest, NextResponse } from "next/server"

// Mock users data for testing
const mockUsers = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "ADMIN",
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
    organization: {
      id: "org-1",
      name: "Organisation Test"
    }
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "USER",
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLoginAt: null,
    organization: {
      id: "org-1",
      name: "Organisation Test"
    }
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "AUDITOR",
    isActive: false,
    createdAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
    organization: null
  }
]

// GET /api/users-test - Get mock users (no auth required)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search")
    const role = searchParams.get("role")
    
    let filteredUsers = [...mockUsers]
    
    // Apply search filter
    if (search) {
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      )
    }
    
    // Apply role filter
    if (role && role !== "all") {
      filteredUsers = filteredUsers.filter(user => user.role === role)
    }
    
    const totalCount = filteredUsers.length
    const totalPages = Math.ceil(totalCount / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex)
    
    return NextResponse.json({
      success: true,
      data: paginatedUsers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    })
  } catch (error) {
    console.error("Error fetching test users:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erreur lors de la récupération des utilisateurs de test" 
      },
      { status: 500 }
    )
  }
}

// POST /api/users-test - Create mock user (no auth required)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const newUser = {
      id: Date.now().toString(),
      name: body.name || "Nouvel utilisateur",
      email: body.email || "<EMAIL>",
      role: body.role || "USER",
      isActive: body.isActive !== undefined ? body.isActive : true,
      createdAt: new Date().toISOString(),
      lastLoginAt: null,
      organization: body.organizationId ? {
        id: body.organizationId,
        name: "Organisation Test"
      } : null
    }
    
    mockUsers.push(newUser)
    
    return NextResponse.json({
      success: true,
      data: newUser,
      message: "Utilisateur de test créé avec succès"
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating test user:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erreur lors de la création de l'utilisateur de test" 
      },
      { status: 500 }
    )
  }
}
