import { NextRequest, NextResponse } from "next/server"
import { AuditService } from "@/lib/services/audit-service"
import { prisma } from "@/lib/prisma"

/**
 * GET /api/audits/test - Test des API audits
 */
export async function GET(request: NextRequest) {
  try {
    // Test de connexion à la base de données
    const userCount = await prisma.user.count()
    const organizationCount = await prisma.organization.count()
    const auditCount = await prisma.audit.count()

    // Test des statistiques
    const stats = await AuditService.getAuditStats()

    return NextResponse.json({
      success: true,
      message: "API Audits fonctionnelle",
      data: {
        database: {
          users: userCount,
          organizations: organizationCount,
          audits: auditCount
        },
        stats
      }
    })

  } catch (error) {
    console.error("Erreur lors du test des API audits:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erreur lors du test",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      },
      { status: 500 }
    )
  }
}
