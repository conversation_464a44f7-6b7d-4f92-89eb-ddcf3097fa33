import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Settings } from "lucide-react"

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Paramètres</h1>
        <p className="text-gray-600">
          Configurez les paramètres de l'application
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Module en développement
          </CardTitle>
          <CardDescription>
            Le module de paramètres sera bientôt disponible
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            Cette section permettra de configurer l'application et les préférences utilisateur.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
