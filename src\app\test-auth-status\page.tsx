"use client"

import { useSimpleAuth, useSimplePermissions } from "@/lib/auth/simple-auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function TestAuthStatusPage() {
  const { user, isAuthenticated, signIn, signOut } = useSimpleAuth()
  const permissions = useSimplePermissions()

  const handleQuickLogin = async () => {
    try {
      await signIn('<EMAIL>', 'password123')
    } catch (error) {
      console.error('Erreur de connexion:', error)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Test État Authentification</h1>
        <div className="space-x-2">
          {!isAuthenticated ? (
            <Button onClick={handleQuickLogin}>
              Connexion Rapide (Admin)
            </Button>
          ) : (
            <Button variant="outline" onClick={signOut}>
              Déconnexion
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* État Authentification */}
        <Card>
          <CardHeader>
            <CardTitle>État Authentification</CardTitle>
            <CardDescription>État actuel de l'authentification</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>Connecté:</strong> 
              <Badge variant={isAuthenticated ? "default" : "secondary"} className="ml-2">
                {isAuthenticated ? "Oui" : "Non"}
              </Badge>
            </div>
            {user && (
              <>
                <div>
                  <strong>Email:</strong> {user.email}
                </div>
                <div>
                  <strong>Nom:</strong> {user.name || "Non défini"}
                </div>
                <div>
                  <strong>Rôle:</strong> 
                  <Badge variant="outline" className="ml-2">
                    {user.role}
                  </Badge>
                </div>
                <div>
                  <strong>Actif:</strong> 
                  <Badge variant={user.isActive ? "default" : "secondary"} className="ml-2">
                    {user.isActive ? "Oui" : "Non"}
                  </Badge>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Permissions Sidebar */}
        <Card>
          <CardHeader>
            <CardTitle>Permissions Sidebar</CardTitle>
            <CardDescription>Permissions pour les éléments du sidebar</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span>audits:read:</span>
              <Badge variant={permissions.checkPermission('audits:read') ? "default" : "secondary"}>
                {permissions.checkPermission('audits:read') ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>observations:read:</span>
              <Badge variant={permissions.checkPermission('observations:read') ? "default" : "secondary"}>
                {permissions.checkPermission('observations:read') ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>reports:read:</span>
              <Badge variant={permissions.checkPermission('reports:read') ? "default" : "secondary"}>
                {permissions.checkPermission('reports:read') ? "✓" : "✗"}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>users:read:</span>
              <Badge variant={permissions.checkPermission('users:read') ? "default" : "secondary"}>
                {permissions.checkPermission('users:read') ? "✓" : "✗"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {!isAuthenticated && (
        <Card>
          <CardHeader>
            <CardTitle>Comptes de Test</CardTitle>
            <CardDescription>Utilisez ces comptes pour tester les permissions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div><strong>Super Admin:</strong> <EMAIL> / password123</div>
              <div><strong>Manager:</strong> <EMAIL> / password123</div>
              <div><strong>Auditeur:</strong> <EMAIL> / password123</div>
              <div><strong>Utilisateur:</strong> <EMAIL> / password123</div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
