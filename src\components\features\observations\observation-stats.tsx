import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { ObservationStats } from "@/lib/validations/observation"
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, Clock, XCircle, FileText, TrendingUp } from "lucide-react"

interface ObservationStatsProps {
  stats: ObservationStats
  loading?: boolean
}

export function ObservationStatsComponent({ stats, loading }: ObservationStatsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const criticalPercentage = stats.total > 0 ? Math.round((stats.criticalCount / stats.total) * 100) : 0
  const resolvedPercentage = stats.total > 0 ? Math.round((stats.resolvedCount / stats.total) * 100) : 0

  const statCards = [
    {
      title: "Total",
      value: stats.total,
      icon: FileText,
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      title: "Ouvertes",
      value: stats.openCount,
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-50"
    },
    {
      title: "Critiques",
      value: stats.criticalCount,
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-50",
      subtitle: `${criticalPercentage}% du total`
    },
    {
      title: "En cours",
      value: stats.byStatus.IN_PROGRESS,
      icon: Clock,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50"
    },
    {
      title: "Résolues",
      value: stats.resolvedCount,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-50",
      subtitle: `${resolvedPercentage}% du total`
    },
    {
      title: "Fermées",
      value: stats.byStatus.CLOSED,
      icon: XCircle,
      color: "text-gray-600",
      bgColor: "bg-gray-50"
    }
  ]

  return (
    <div className="space-y-6">
      {/* Cartes principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {statCards.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                    <Icon className={`h-4 w-4 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    {stat.subtitle && (
                      <p className="text-xs text-gray-500">{stat.subtitle}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Répartition détaillée */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Par sévérité */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Répartition par sévérité
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { key: 'CRITICAL', label: 'Critique', color: 'bg-red-500', textColor: 'text-red-700' },
                { key: 'HIGH', label: 'Élevée', color: 'bg-orange-500', textColor: 'text-orange-700' },
                { key: 'MEDIUM', label: 'Moyenne', color: 'bg-yellow-500', textColor: 'text-yellow-700' },
                { key: 'LOW', label: 'Faible', color: 'bg-blue-500', textColor: 'text-blue-700' }
              ].map((severity) => {
                const count = stats.bySeverity[severity.key as keyof typeof stats.bySeverity]
                const percentage = stats.total > 0 ? Math.round((count / stats.total) * 100) : 0
                
                return (
                  <div key={severity.key} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${severity.color}`}></div>
                      <span className="text-sm font-medium">{severity.label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-bold ${severity.textColor}`}>{count}</span>
                      <span className="text-xs text-gray-500">({percentage}%)</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Par statut */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Répartition par statut
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { key: 'OPEN', label: 'Ouverte', color: 'bg-red-500', textColor: 'text-red-700' },
                { key: 'IN_PROGRESS', label: 'En cours', color: 'bg-yellow-500', textColor: 'text-yellow-700' },
                { key: 'RESOLVED', label: 'Résolue', color: 'bg-green-500', textColor: 'text-green-700' },
                { key: 'CLOSED', label: 'Fermée', color: 'bg-gray-500', textColor: 'text-gray-700' },
                { key: 'REJECTED', label: 'Rejetée', color: 'bg-purple-500', textColor: 'text-purple-700' }
              ].map((status) => {
                const count = stats.byStatus[status.key as keyof typeof stats.byStatus]
                const percentage = stats.total > 0 ? Math.round((count / stats.total) * 100) : 0
                
                return (
                  <div key={status.key} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${status.color}`}></div>
                      <span className="text-sm font-medium">{status.label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-bold ${status.textColor}`}>{count}</span>
                      <span className="text-xs text-gray-500">({percentage}%)</span>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
